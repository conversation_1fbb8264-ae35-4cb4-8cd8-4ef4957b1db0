package com.ctrip.pay.mr.servicecode.serialize;

public class BatchIdVerificationDto {
    private String VerifyServiceType;
    private String IdNumber ;

    private String IdName ;

    /// <summary>
    ///   0 校验通过
    /// 1001 校验不通过
    /// 1002 身份证号码错误
    /// 1003 未及时获取结果，待验证
    /// 4000 查询参数非法
    /// 4100 token错误
    /// 4200 签名错误
    /// 4300 token没有此接口的查询权限
    /// 4400 IP不在白名单
    /// 5000 服务内部错误
    /// </summary>
    private String VerifyCode ;

    private String VerifyMsg ;

    public String getVerifyServiceType() {
        return VerifyServiceType;
    }

    public void setVerifyServiceType(String verifyServiceType) {
        VerifyServiceType = verifyServiceType;
    }

    public String getIdNumber() {
        return IdNumber;
    }

    public void setIdNumber(String idNumber) {
        IdNumber = idNumber;
    }

    public String getIdName() {
        return IdName;
    }

    public void setIdName(String idName) {
        IdName = idName;
    }

    public String getVerifyCode() {
        return VerifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        VerifyCode = verifyCode;
    }

    public String getVerifyMsg() {
        return VerifyMsg;
    }

    public void setVerifyMsg(String verifyMsg) {
        VerifyMsg = verifyMsg;
    }
}
