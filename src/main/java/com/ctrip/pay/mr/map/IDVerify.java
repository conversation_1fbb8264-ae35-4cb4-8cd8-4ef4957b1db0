package com.ctrip.pay.mr.map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ctrip.pay.mr.servicecode.serialize.BatchIdVerificationDto;
import com.ctrip.pay.mr.servicecode.serialize.BatchIdVerificationRequestType;
import com.ctrip.pay.mr.servicecode.serialize.BatchIdVerificationResponseType;
import com.ctrip.pay.mr.servicecode.serialize.IdVerificationDto;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTime;
//import sun.misc.BASE64Encoder;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class IDVerify {
    private static final String MAC_NAME = "HmacSHA1";
    private static final String ENCODING = "UTF-8";
    /*本地测试数据
           static String token = "juoq4wfIaDHL9ymm3e1KgxdhpuM=";
           static String secKey = "Eab+pmzBzbq0jySlUn2DnTvBF6Q=";
           static String apiUrl = "http://ws.ctripgroupgateway.fws.qa.nt.ctripcorp.com/infosec/idcard/batchIdVerification"; //批量，最大50条
     */
    static String apiUrl = "http://ctrip.ctripgroup.cn/infosec/idcard/batchIdVerification"; //批量，最大50条
    //只验信安本地库的token
    static String offlineToken = "ZHN8WVBmnJyRQuGY0G8jQZ7LV70=";
    static String offlineSecKey = "ZD5Bs1RjSbFFZOJdEYnpDitFZSI=";

    /*只验证生产库token*/
    static String onlineToken = "Dzh5Q3Q+FnbDOfqY5R6VyrrP+zs=";
    static String onlineSecKey = "8HJV6sHybXCPUAb582wtT7Jz730=";

    public static void main(String[] args) {
        String str="[{\"IdName\":\"罗建香\",\"IdNumber\":\"******************\"},{\"IdName\":\"罗建香\",\"IdNumber\":\"******************\"}]";
        List<BatchIdVerificationDto> ret = new ArrayList<>();
        List<IdVerificationDto> param = JSONArray.parseArray(str, IdVerificationDto.class);
        try {
            ret=check(param,true);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        //String str="{\"responseStatus\":{\"timestamp\":\"/Date(1597798692545+0800)/\",\"ack\":\"Success\",\"errors\":[]},\"batchIdVerificationDtos\":[{\"idNumber\":\"511028198508140017\",\"idName\":\"林传雨\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"350212199402060557\",\"idName\":\"朱志翔\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"412724199301166978\",\"idName\":\"李国军\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"652823199809042240\",\"idName\":\"李霞\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"522632198202102595\",\"idName\":\"石成柱\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"110105196401265414\",\"idName\":\"沈志坚\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"450202198302280321\",\"idName\":\"朱俊仪\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"370125198201043019\",\"idName\":\"李国庆\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"431002199903201511\",\"idName\":\"匡旭\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"622323199310170523\",\"idName\":\"于斐\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"342921198811102746\",\"idName\":\"汪静\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"511025198211196264\",\"idName\":\"左晓梅\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"511526199209051726\",\"idName\":\"袁建梅\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"412326198804297538\",\"idName\":\"左鹏鹏\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"421023199411170026\",\"idName\":\"刘娟\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"342222198208138013\",\"idName\":\"朱克峰\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"410527199202255816\",\"idName\":\"翟帅虎\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"320586198609302964\",\"idName\":\"刘菁\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"350524199202173567\",\"idName\":\"陈晓芳\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"321084198804151517\",\"idName\":\"唐勇\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"210504198212010285\",\"idName\":\"杨丽\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"430781198701110548\",\"idName\":\"周嵘\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"420602198408170020\",\"idName\":\"王丹\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"430111197807173735\",\"idName\":\"彭志龙\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"440582199606030947\",\"idName\":\"林欢\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"654123199610122810\",\"idName\":\"包明军\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"422225196312270030\",\"idName\":\"张国志\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"431121199610176521\",\"idName\":\"曾剑\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"360124199406193615\",\"idName\":\"王力\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"232303197606190657\",\"idName\":\"张联元\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"610528198807050310\",\"idName\":\"朱明\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"370811198809293024\",\"idName\":\"汪利娜\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"110105196409247114\",\"idName\":\"付秋立\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"51152419840504274X\",\"idName\":\"樊成莲\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"230102198909025216\",\"idName\":\"耿喜东\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"370782198606071456\",\"idName\":\"徐培瑜\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"622102199509091424\",\"idName\":\"关雯璐\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"430624199309179155\",\"idName\":\"肖畅\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"512225197708154526\",\"idName\":\"刘群\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"310101196003223616\",\"idName\":\"颜广斌\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"320302197006133214\",\"idName\":\"李杰\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"321002198911294383\",\"idName\":\"沈梦甜\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"362330198811113661\",\"idName\":\"王秀\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"142429197110200839\",\"idName\":\"杜运炜\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"13063219701201101X\",\"idName\":\"袁利宾\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"620123198602245147\",\"idName\":\"张彩霞\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"},{\"idNumber\":\"320582198107218521\",\"idName\":\"顾逸群\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"370921198102011271\",\"idName\":\"臧海生\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"1004\",\"verifyMsg\":\"身份证号码不在本地存储中\"},{\"idNumber\":\"513525196902164552\",\"idName\":\"任昌川\",\"extension\":\"\",\"verifyServiceType\":\"10001\",\"verifyCode\":\"0\",\"verifyMsg\":\"校验通过\"}],\"retCode\":\"0\",\"retMsg\":\"成功\"}";
        // BatchIdVerificationResponseType response = JSON.parseObject(str, BatchIdVerificationResponseType.class);
        for(BatchIdVerificationDto ins:ret) {
            System.out.println(ins.getVerifyCode()+" : "+ins.getVerifyMsg());
        }
        System.exit(0);
    }

    public static List<BatchIdVerificationDto> check(List<IdVerificationDto> userList,boolean isOnline) throws UnsupportedEncodingException {
        if(userList==null){
            return null;
        }
        String body;
        List<BatchIdVerificationDto> result=new ArrayList<>();

        BatchIdVerificationRequestType request;
        BatchIdVerificationResponseType response;

        request = createRequest(userList,isOnline);

        CloseableHttpClient client = HttpClientBuilder.create().build();
        HttpPost post = new HttpPost(apiUrl);
        HttpResponse resp = null;

        post.addHeader("Content-Type", "application/json");
        post.setEntity(new StringEntity(JSON.toJSONString(request), Charset.forName("UTF-8")));

        try {
            resp=client.execute(post);

            if(resp != null) {
                body = EntityUtils.toString(resp.getEntity());
//                System.out.println(body);
                response = JSON.parseObject(body, BatchIdVerificationResponseType.class);
                if (response!=null&&response.getRetCode().equals("0")) {
                    result=response.getBatchIdVerificationDtos();
                }else {
//                    System.out.println(response.getRetMsg());
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }

        return result;
    }

    private static BatchIdVerificationRequestType createRequest(List<IdVerificationDto> userList,boolean isOnline)
    {
        BatchIdVerificationRequestType request = new BatchIdVerificationRequestType();
        request.setExtension("");
        request.setBatchIdVerificationDtos(userList);

        // 随机数
        request.setNonce(new Random().nextInt());
        // 请求时间戳，单位为s。开发者需要将请求服务器时间校准为标准时间。
        request.setTimestamp(DateTime.now().toDateTime().getMillis());
        request.setToken(offlineToken);
        if(isOnline){
            request.setToken(onlineToken);
        }

        // 生成签名
        StringBuilder stringBuffer = new StringBuilder();
        if (StringUtils.isNotEmpty(request.getExtension()))
        {
            stringBuffer.append(request.getExtension());
        }
        for (IdVerificationDto userInfo : userList) {
            stringBuffer.append(userInfo.getIdName());
            stringBuffer.append(userInfo.getIdNumber());
        }

        stringBuffer.append(request.getNonce());
        stringBuffer.append(request.getTimestamp());
        stringBuffer.append(request.getToken());

        String sign = null;
        try {
            String finalKey = isOnline == true ? onlineSecKey : offlineSecKey;
            sign = encryptBASE64(hmacSHA1Encrypt(stringBuffer.toString().getBytes("UTF-8"),finalKey));
        } catch (Exception e) {
            e.printStackTrace();
        }
        request.setSign(sign);

        return request;
    }

    /**
     * 使用 HMAC-SHA1 签名方法对对encryptText进行签名
     *
     * @param data       被签名的数据
     * @param encryptKey 密钥
     * @return 密钥
     * @throws Exception
     */
    public static byte[] hmacSHA1Encrypt(byte[] data, String encryptKey) throws Exception {
        byte[] key = encryptKey.getBytes(ENCODING);
        // 根据给定的字节数组构造一个密钥,第二参数指定一个密钥算法的名称
        SecretKey secretKey = new SecretKeySpec(key, MAC_NAME);
        // 生成一个指定 Mac 算法 的 Mac 对象
        Mac mac = Mac.getInstance(MAC_NAME);
        // 用给定密钥初始化 Mac 对象
        mac.init(secretKey);
        // 完成 Mac 操作
        return mac.doFinal(data);
    }

    public static String encryptBASE64(byte[] key) throws Exception {
//        return (new BASE64Encoder()).encode(key);
        return  "";
    }

}


