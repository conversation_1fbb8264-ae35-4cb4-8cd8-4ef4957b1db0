package com.ctrip.pay.mr.servicecode.serialize;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.pay.mr.utils.JsonObjectUtil;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.log4j.Logger;

import java.util.Date;

/**
 * Created by ymfang on 2019/7/19.
 */
public class TraceModel {
    private static final Logger log = Logger.getLogger(TraceModel.class);

    private String serviceName;
    private Object requestHead;
    private Object request;
    private Object response;
    private Date beginTime;
    private Date endTime;
    private long timeSpanCal;
    private long timeSpanRaw;
    private String appId;
    private Date startTime;



    public TraceModel() {
    }

    public TraceModel(String serviceName, Object requestHead, Object request, Object response, Date beginTime,
                      Date endTime, long timespan) {
        this.serviceName = serviceName;
        this.requestHead = requestHead;
        this.request = request;
        this.response = response;
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.timeSpanCal = calTimeSpan();
        this.timeSpanRaw = timespan;
    }

    public TraceModel(String serviceName, Object requestHead, Object request, Object response, Date beginTime,
                      Date endTime, String appId, long timespan) {
        this.serviceName = serviceName;
        this.requestHead = requestHead;
        this.request = request;
        this.response = response;
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.appId = appId;
        this.timeSpanCal = calTimeSpan();
        this.timeSpanRaw = timespan;
    }

    public int getPlat() {
        int plat = -1;//默认平台返回
        if (request != null) {
            String reqStr = JSON.toJSONString(request);

            try {
                JsonObject req = new JsonParser().parse(reqStr).getAsJsonObject();
                plat = JsonObjectUtil.getIntObject(req, "Platform");
                if (plat == -1) {
                    plat = JsonObjectUtil.getIntObject(req, "plat");
                }
            } catch (Exception e) {
//                log.info("plat 解析失败:" + request);
            }

        }
        return plat;
    }

    public Double getAmount(String servicecode) {
        Double amount = -1.0;//默认返回

        if ("31100102".equals(servicecode) && response != null && response.toString().indexOf("ResponseInfo101")>0 && response.toString().indexOf("PayOrderInfo")>0) {
            JSONObject m = JSON.parseObject(response.toString());
            try {
                JSONObject PayOrderInfo = m.getJSONObject("ResponseInfo101").getJSONObject("PayOrderInfo");
                if(PayOrderInfo !=null){
                    JSONObject order = PayOrderInfo.getJSONObject("order");
                    if(order!=null)
                        amount= order.getDouble("orderAmount")!=null ? order.getDouble("orderAmount") : -1.0;
                    else if(PayOrderInfo.getDouble("orderAmount") !=null)
                        amount= PayOrderInfo.getDouble("orderAmount");
                }

            }catch(Exception ex){

            }
        }

        if ("31012001".equals(servicecode) && response != null && response.toString().indexOf("orderAmount")>0) {
            JSONObject m = JSON.parseObject(response.toString());
            try {
                JSONObject order = m.getJSONObject("order");
                if(order !=null){
                    amount= order.getDouble("orderAmount")!=null ? order.getDouble("orderAmount") : -1.0;
                }

            }catch(Exception ex){

            }
        }

        if ("31002002".equals(servicecode) && response != null && response.toString().indexOf("OrderAmount")>0) {
            JSONObject m = JSON.parseObject(response.toString());
            try {
                JSONObject order = m.getJSONObject("OrderInfo");
                if(order !=null){
                    amount= order.getDouble("OrderAmount")!=null ? order.getDouble("OrderAmount") : -1.0;
                }

            }catch(Exception ex){

            }
        }

        if ( ("31004001".equals(servicecode) || "31004002".equals(servicecode))
                && request != null && request.toString().indexOf("oamount")>0) {
            JSONObject m = JSON.parseObject(request.toString());
            try {
                JSONObject order = m.getJSONObject("oinfo");
                if(order !=null){
                    amount= order.getDouble("oamount")!=null ? order.getDouble("oamount") : -1.0;
                }

            }catch(Exception ex){

            }
        }

        return amount;
    }

    //for servicecode='31100102'
    //补充bustype coalesce(get_json_object(message,'$.response.ResponseInfo101.PayOrderInfo.merchant.busType')
    // ,get_json_object(message,'$.response.ResponseInfo101.PayOrderInfo.busType'))
    public String getBustype() {
        String bustype = "-1";//默认平台返回
        if (response != null && response.toString().indexOf("ResponseInfo101")>0 && response.toString().indexOf("PayOrderInfo")>0) {
            JSONObject m = JSON.parseObject(response.toString());
            try {
                JSONObject PayOrderInfo = m.getJSONObject("ResponseInfo101").getJSONObject("PayOrderInfo");
                if(PayOrderInfo !=null){
                    JSONObject merchant = PayOrderInfo.getJSONObject("merchant");
                    if(merchant!=null)
                        bustype= merchant.getString("busType")!=null ? merchant.getString("busType") : "-1";
                    else if(PayOrderInfo.getString("busType") !=null)
                        bustype= PayOrderInfo.getString("busType");
                }

            }catch(Exception ex){
               // log.info("bustype 解析失败:" + response);
            }
        }
        return bustype;
    }

    //获取服务调用情况
    public int getResultcode() {
        int resultcode = -1;
        if (response != null) {
            try {
                String resStr = JSON.toJSONString(response);
                JsonObject res = new JsonParser().parse(resStr).getAsJsonObject();
                resultcode = JsonObjectUtil.getIntObject(res, "ResultCode");
                if (resultcode == -1){
                    resultcode = JsonObjectUtil.getIntObject(res, "rc");
                }
                if (resultcode == -1){
                    resultcode = JsonObjectUtil.getIntObject(res, "Result");
                }
                //for servicecode=31012002
                if (resultcode == -1){
                    JsonObject jsonObject = JsonObjectUtil.getJsonObject(res, "header");
                    resultcode = JsonObjectUtil.getIntObject(jsonObject, "resultCode");
                }

            } catch (Exception e) {
              //  log.info("resultcode 解析失败:" + response);

            }
        }

        return resultcode;
    }

    //获取request's element
    public String getRequestElement(String name) {
        String ret = "";
        if (request != null){
            try{
                String reqStr = JSON.toJSONString(request);
                JsonObject req = new JsonParser().parse(reqStr).getAsJsonObject();
                ret = JsonObjectUtil.getStrObject(req, name);
            } catch(Exception e){
                log.info("request 序列化失败:" + request);
            }
        }
        return ret;
    }


    public long calTimeSpan() {
        return endTime.getTime() - beginTime.getTime();
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public Object getRequestHead() {
        return requestHead;
    }

    public void setRequestHead(Object requestHead) {
        this.requestHead = requestHead;
    }

    public Object getRequest() {
        return request == null ? "{}" : request;
    }

    public void setRequest(Object request) {
        this.request = request;
    }

    public Object getResponse() {
        return response == null ? "{}" : response;
    }

    public void setResponse(Object response) {
        this.response = response;
    }

    public Date getBeginTime() {
        return beginTime == null ? startTime : beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public long getTimeSpanCal() {
        return timeSpanCal;
    }

    public void setTimeSpanCal(long timeSpanCal) {
        this.timeSpanCal = timeSpanCal;
    }

    public long getTimeSpanRaw() {
        return timeSpanRaw;
    }

    public void setTimeSpanRaw(long timeSpanRaw) {
        this.timeSpanRaw = timeSpanRaw;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    @Override
    public String toString() {
        return "TraceModel{" +
                "serviceName='" + serviceName + '\'' +
                ", requestHead=" + requestHead +
                ", request=" + request +
                ", response=" + response +
                ", beginTime=" + beginTime +
                ", endTime=" + endTime +
                ", timeSpanCal=" + timeSpanCal +
                ", timeSpanRaw=" + timeSpanRaw +
                ", appId='" + appId + '\'' +
                '}';
    }

    public static void main(String[] args) {

        String bodyStr = "{\"Response\":\"{\\\"rc\\\":0,\\\"subcode\\\":null,\\\"dbgmsg\\\":null,\\\"rmsg\\\":null,\\\"oid\\\":1901083554,\\\"oidex\\\":***********,\\\"bilno\\\":\\\"1331038001616727\\\",\\\"seqid\\\":\\\"3119082422000073232\\\",\\\"cardinfoid\\\":0,\\\"sphone\\\":null,\\\"thirdpartyinfo\\\":{\\\"sig\\\":\\\"<?xml version=\\\\\\\"1.0\\\\\\\"?><Request><Header/><MobilePayRequest><OrderID>***********</OrderID><PaymentWayID>WechatScanCode</PaymentWayID><Amount>10.00</Amount><AppId>wxa3ca64b5aa2a79a7</AppId><PartnerId>1235410702</PartnerId><PrepayId>wx24223532583346ecfdf8602a1499605200</PrepayId><NonceStr>7bfeec29b0644e8d9cddcb47011a495e</NonceStr><TimeStamp>1566657332</TimeStamp><PackageValue>Sign=WXPay</PackageValue><Sign>CAA101A14B5D9FBA5A5BE454B49B50FD11ADA12652CB5A12D30E661F31A67736</Sign><PayRequestExtend></PayRequestExtend></MobilePayRequest></Request>\\\",\\\"type\\\":0,\\\"referenceno\\\":null},\\\"riskcode\\\":null,\\\"refid\\\":null,\\\"insdetail\\\":null,\\\"orderlefttime\\\":7199,\\\"thresholdtime\\\":600,\\\"streamcontrbit\\\":0,\\\"pwdpayguideinterval\\\":\\\"0\\\",\\\"identityver\\\":0,\\\"weixindaifuurl\\\":null,\\\"discamount\\\":0,\\\"pdiscinfos\\\":null,\\\"threedsinfos\\\":null,\\\"threedsrefid\\\":null,\\\"nativewechaturl\\\":null,\\\"fnccouponresult\\\":null}\",\"Timespan\":829,\"EndTime\":\"2019/08/24 22:35:32\",\"ServiceName\":\"********\",\"Request\":\"{\\\"ver\\\":801,\\\"plat\\\":2,\\\"requestid\\\":\\\"1908241435243452jqe\\\",\\\"actkey\\\":\\\"\\\",\\\"actcount\\\":0,\\\"lastpaytm\\\":\\\"0001/01/01 00:00:00\\\",\\\"statistic\\\":\\\"1\\\",\\\"opttype\\\":1,\\\"BusinessETypePB\\\":4,\\\"bustype\\\":4,\\\"usetype\\\":1,\\\"subusetype\\\":0,\\\"subpay\\\":0,\\\"opadbitmp\\\":1,\\\"forcardfee\\\":0.0,\\\"forcardcharg\\\":0,\\\"stype\\\":0,\\\"rvcode\\\":\\\"\\\",\\\"seqid\\\":\\\"3119082422000073232\\\",\\\"openid\\\":\\\"\\\",\\\"rmstoken\\\":\\\"\\\",\\\"paytype\\\":4,\\\"geos\\\":null,\\\"payrestrict\\\":{\\\"paytypelist\\\":110,\\\"subpaytypelist\\\":470613967,\\\"blacklist\\\":null,\\\"whitelist\\\":null,\\\"cardnumseglist\\\":null,\\\"defaultpaytype\\\":0,\\\"restrictbit\\\":0,\\\"discountidlist\\\":\\\"\\\",\\\"thirdrestrictlist\\\":null,\\\"senamelist\\\":\\\"SE UnionPay,\\\",\\\"discblacklist\\\":null},\\\"oinfo\\\":{\\\"oid\\\":1901083554,\\\"oidex\\\":***********,\\\"odesc\\\":\\\"平南南-珠海\\\",\\\"currency\\\":\\\"CNY\\\",\\\"oamount\\\":10.0,\\\"extno\\\":\\\"\\\",\\\"autoalybil\\\":true,\\\"notify\\\":\\\"\\\",\\\"recall\\\":\\\"\\\",\\\"partdiscamount\\\":0.0},\\\"tktinfo\\\":null,\\\"cardinfo\\\":null,\\\"thirdpartyinfo\\\":{\\\"paymentwayid\\\":\\\"WechatScanCode\\\",\\\"brandid\\\":\\\"WechatScanCode\\\",\\\"brandtype\\\":\\\"2\\\",\\\"channelid\\\":\\\"28\\\",\\\"typeid\\\":0,\\\"subtypeid\\\":0,\\\"typecode\\\":\\\"\\\",\\\"thirdcardnum\\\":\\\"\\\",\\\"amount\\\":10.0,\\\"PrimThirdAmount\\\":0.0,\\\"thirdfee\\\":0,\\\"Code\\\":null,\\\"MktOpenid\\\":null,\\\"proid\\\":\\\"\\\",\\\"extend\\\":\\\"\\\",\\\"weixinConfigSupport\\\":false,\\\"extendjson\\\":\\\"\\\",\\\"exchag\\\":\\\"\\\",\\\"couponid\\\":\\\"\\\",\\\"chargemode\\\":0,\\\"collectionid\\\":null,\\\"returnurl\\\":null},\\\"cashinfo\\\":null,\\\"walletpayinfo\\\":null,\\\"guarantee\\\":null,\\\"fncexpayway\\\":null,\\\"touchpay\\\":{\\\"deviceinfo\\\":{\\\"devguid\\\":\\\"\\\",\\\"devmod\\\":\\\"\\\",\\\"wifimac\\\":\\\"\\\",\\\"imei\\\":\\\"\\\",\\\"vendorid\\\":\\\"\\\",\\\"keyguid\\\":\\\"\\\"},\\\"keyguid\\\":\\\"\\\",\\\"token\\\":\\\"\\\"},\\\"extend\\\":null,\\\"cdinfo\\\":{\\\"cdpaymentwayid\\\":\\\"\\\",\\\"cdbrandid\\\":\\\"\\\",\\\"cdchannelid\\\":\\\"\\\",\\\"cdfuct\\\":0,\\\"cdpayamout\\\":0.0,\\\"paswd\\\":\\\"\\\",\\\"chargemode\\\":0},\\\"SourceInfo\\\":null,\\\"userauthinfolist\\\":null,\\\"insinfos\\\":null,\\\"extendparam\\\":\\\"\\\",\\\"passport\\\":{\\\"pwd\\\":\\\"\\\",\\\"touchpay\\\":{\\\"deviceinfo\\\":{\\\"devguid\\\":\\\"\\\",\\\"devmod\\\":\\\"\\\",\\\"wifimac\\\":\\\"\\\",\\\"imei\\\":\\\"\\\",\\\"vendorid\\\":\\\"\\\",\\\"keyguid\\\":\\\"\\\"},\\\"keyguid\\\":\\\"\\\",\\\"token\\\":\\\"\\\"},\\\"frtoken\\\":null},\\\"attach\\\":\\\"\\\",\\\"uisflag\\\":48,\\\"streamcontrbit\\\":0,\\\"orderlatestavailbletime\\\":\\\"\\\",\\\"opbitmap\\\":\\\"\\\",\\\"sdiscinfo\\\":{\\\"disckey\\\":\\\"\\\",\\\"disctype\\\":0,\\\"discamount\\\":0,\\\"distitle\\\":\\\"\\\",\\\"extend\\\":\\\"\\\",\\\"RuleId\\\":null,\\\"RealDiscountAmount\\\":0.0,\\\"DiscountStatus\\\":0,\\\"SelectedCardinfoId\\\":null},\\\"myaccountinfo\\\":{\\\"name\\\":\\\"\\\",\\\"idtype\\\":0,\\\"idnumber\\\":\\\"\\\"},\\\"AppToken\\\":null,\\\"scenarioinfo\\\":null,\\\"h5plat\\\":0,\\\"paytoken\\\":null}\",\"StartTime\":\"2019/08/24 22:35:31\",\"RequestHead\":\"{\\\"Encoding\\\":2,\\\"SystemCode\\\":\\\"32\\\",\\\"Language\\\":\\\"01\\\",\\\"UserId\\\":\\\"M2685430860\\\",\\\"ClientId\\\":\\\"32001020110090258277\\\",\\\"ClientToken\\\":\\\"8f42QqeJgkMasGuJtAdA\\\",\\\"ClientVersion\\\":801.003,\\\"SourceId\\\":\\\"8013\\\",\\\"ExSourceID\\\":null,\\\"ServiceCode\\\":\\\"********\\\",\\\"MessageNumber\\\":\\\"*****************\\\",\\\"NewMessageNumber\\\":\\\"*****************\\\",\\\"AuthToken\\\":\\\"F544F620AC3D405369523079BCC65D7F559A2D20033E892C205B645B7718FEBB\\\",\\\"ExtentionItemList\\\":[{\\\"Key\\\":\\\"htl-traveltype\\\",\\\"Value\\\":\\\"65584\\\"},{\\\"Key\\\":\\\"Flt_SessionId\\\",\\\"Value\\\":\\\"32\\\"},{\\\"Key\\\":\\\"htl-up\\\",\\\"Value\\\":\\\"eyJjdCI6MywibG4iOjExMC40MTI1MjcsImFjIjo0MCwibGEiOjIzLjUzOTc0Nywib2YiOjI4ODAwLCJ0biI6IuWMl+S6rOaXtumXtCIsInRpZCI6MzMzLCJ0cyI6MTU2NjY1NzExODYxNiwiY2lkIjoiNzUzOCJ9\\\"},{\\\"Key\\\":\\\"htl-customertype\\\",\\\"Value\\\":\\\"7\\\"},{\\\"Key\\\":\\\"htl-userstarlevel\\\",\\\"Value\\\":\\\"M\\\"},{\\\"Key\\\":\\\"htl-pressionId\\\",\\\"Value\\\":\\\"70ea41a3-0616-48ca-bd66-f093d886bee4\\\"},{\\\"Key\\\":\\\"htl-userrewardtype\\\",\\\"Value\\\":\\\"0\\\"},{\\\"Key\\\":\\\"htl-sessionId\\\",\\\"Value\\\":\\\"5899645c-a73c-4272-9233-bf1f7f12eb1c\\\"},{\\\"Key\\\":\\\"pageId\\\",\\\"Value\\\":\\\"widget_pay_main\\\"},{\\\"Key\\\":\\\"vid\\\",\\\"Value\\\":\\\"47910F60E88A11E898C6C17C07006742\\\"},{\\\"Key\\\":\\\"pvid\\\",\\\"Value\\\":\\\"776\\\"},{\\\"Key\\\":\\\"sid\\\",\\\"Value\\\":\\\"32\\\"}],\\\"AppId\\\":\\\"99999999\\\",\\\"Extention\\\":null,\\\"RemoteIPAddress\\\":\\\"**************\\\",\\\"IsSuccessful\\\":false,\\\"RecordCount\\\":0,\\\"IsLinger\\\":true,\\\"Platform\\\":3,\\\"Auth\\\":null}\",\"Result\":false,\"IsSuccessful\":true}";
        TraceModel model = JSON.parseObject(bodyStr, TraceModel.class);
        //System.out.println(model.getRequest());

        String resStr = JSON.toJSONString(model.getBeginTime());
        JsonObject res = new JsonParser().parse(resStr).getAsJsonObject();
        JsonObject jsonObject = JsonObjectUtil.getJsonObject(res,"ResponseInfo101");
        jsonObject = JsonObjectUtil.getJsonObject(jsonObject,"PayOrderInfo");
        jsonObject = JsonObjectUtil.getJsonObject(jsonObject,"merchant");
        String strVal = JsonObjectUtil.getStrObject(jsonObject,"busType");
        System.out.println(strVal);
//        String reqStr = JSON.toJSONString(request);
//        System.out.println(reqStr);
//        JsonObject req = new JsonParser().parse(reqStr).getAsJsonObject();
//        int plat = JsonObjectUtil.getIntObject(req, "Platform");
//        TraceModel tm = new TraceModel();
//        tm.setRequest(request);
//        System.out.println(tm.getPlat());
    }
}
