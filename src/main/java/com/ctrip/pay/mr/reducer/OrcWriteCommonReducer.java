package com.ctrip.pay.mr.reducer;

import com.alibaba.fastjson.JSON;
import com.ctrip.pay.mr.map.ComposedKey;
import com.ctrip.pay.mr.servicecode.serialize.MessageParser;
import com.ctrip.pay.mr.servicecode.serialize.TagParser;
import com.ctrip.pay.mr.servicecode.serialize.TraceModel;
import com.ctrip.pay.mr.utils.DateUtil;
import com.ctrip.pay.mr.utils.MRUtils;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcMap;
import org.apache.orc.mapred.OrcStruct;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: ymfang
 * @Date: 2019/7/18 14:52
 */
public class OrcWriteCommonReducer
        extends Reducer<ComposedKey, Text, NullWritable, OrcStruct> {
    private int count = 0;
    private String schema = "";
    private MultipleOutputs<NullWritable, OrcStruct> multipleOutputs = null;
    private List<String> fieldList = new ArrayList<>();
    //对应orc表数据结构
    private OrcStruct pair = null;
    public static String schemaPath = "ORCDescription/servicecode.properties";

    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        multipleOutputs = new MultipleOutputs(context);
        fieldList = MRUtils.initalFields(schemaPath);
        schema = MRUtils.acquireRawSchema(schemaPath);
        pair = (OrcStruct) OrcStruct.createValue(TypeDescription.fromString(schema));
    }

    @Override
    protected void reduce(ComposedKey key, Iterable<Text> values, Context context) throws IOException, InterruptedException {
        if ((count++) < 3) {
            System.out.println("InnerReducer:" + count + ": uid=" + key.toString());
        }
        String servicecode = key.getServicecode();
        String day = key.getDay();
        for (Text itm : values) {
            if ((count++) < 3) {
                System.out.println("InnerReducer:" + count + ": uid=" + key + "  value:" + itm.toString());
            }

            //解析msg
            Map<String, String> tagsMap = TagParser.parseTags(itm.toString());
            String bodyStr = MessageParser.parseBody(itm.toString());
            TraceModel model = null;
            try {
                model = JSON.parseObject(bodyStr, TraceModel.class);
            } catch (Exception e) {

            }
            if (model == null || tagsMap.size() == 0) {
                return;
            }
            //计算出plat 和resultcode
            if (!tagsMap.containsKey("plat")) {
                tagsMap.put("plat", model.getPlat() + "");
            }
            tagsMap.put("resultcode", model.getResultcode() + "");

            //赋值uid、orderid、bustype,syscode
            StringBuilder sb = new StringBuilder();
            try {
                for (int i = 0; i < fieldList.size(); i++) {
                    String str = fieldList.get(i);
                    if (str.equals("tag")) {
                        OrcMap orcMap = (OrcMap) pair.getFieldValue(i);
                        if (tagsMap.size() > 0) {
                            for (String elem : tagsMap.keySet()) {
                                orcMap.put(new Text(elem), new Text(tagsMap.get(elem)));
                            }
                        } else {
                            orcMap.put(new Text(""), new Text(""));
                            pair.setFieldValue(i, orcMap);

                        }
                        pair.setFieldValue(i, orcMap);
                        continue;
                    }

                    String strVal = tagsMap.get(str);
                    if (str.equals("request")) {
                        strVal = JSON.toJSONString(model.getRequest());
                    } else if (str.equals("response")) {
                        strVal = JSON.toJSONString(model.getResponse());
                    } else if (str.equals("requesttime")) {
                        strVal = DateUtil.dateFormat.format(model.getBeginTime());
                    } else if (str.equals("message")) {
                        strVal = itm.toString();
                    } else if (str.equals("message_raw")) {
                        strVal = itm.toString();
                    }
                    sb.append(str + " = " + strVal);
                    if (strVal == null) {
                        strVal = "";
                    }
                    pair.setFieldValue(i, new Text(strVal));
                }
                if (servicecode != null && day != null) {
                    multipleOutputs.write(servicecode, NullWritable.get(), pair, String.format("servicecode=%s/dt=%s/", servicecode, day));
                }
            } catch (Exception exp) {
                System.out.println("error:" + itm);
            }

        }

    }

    @Override
    public void cleanup(Context context) throws IOException, InterruptedException {
        multipleOutputs.close();
    }

    public static void main(String[] args) {
        String servicecode = "310021";
        String day = "2019-08-30";
        String str = String.format("servicecode=%s/dt=%s/", servicecode, day);
        System.out.println(str);
    }
}
