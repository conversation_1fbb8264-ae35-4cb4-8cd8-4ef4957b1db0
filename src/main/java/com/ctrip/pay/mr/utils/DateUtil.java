package com.ctrip.pay.mr.utils;

import com.google.common.collect.Lists;
import org.joda.time.DateTime;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class DateUtil {
    public static String[] formats = {"yyyy-MM-dd HH:mm:ss"};
    public static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

    public static String format(Date date) {
        if (date == null) {
            date = new Date();
        }

        return dateFormat.format(date);

    }

    public static Long getDateLong(Date date) {
        if (date == null) {
            date = new Date();
        }

        return date.getTime();

    }

    public static Date getDateLong(String str) {
        Date date = new Date();
        if (str != null) {
            date = new Date(Timestamp.valueOf(str).getTime());
        }

        return date;

    }

    public static StringBuilder format(long timestamp) {
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(timestamp);
        StringBuilder key = new StringBuilder();
        int y = c.get(1);
        key.append(y).append("/");
        int m = c.get(2) + 1;
        if (m < 10) {
            key.append("0").append(m);
        } else {
            key.append(m);
        }
        key.append("/");
        int d = c.get(5);
        if (d < 10) {
            key.append("0").append(d);
        } else {
            key.append(d);
        }
        key.append("/");
        int h = c.get(11);
        if (h < 10) {
            key.append("0").append(h);
        } else {
            key.append(h);
        }
        return key;
    }

    public static StringBuilder formatTime(long timestamp) {
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(timestamp);
        StringBuilder key = new StringBuilder();
        int y = c.get(1);
        key.append(y).append("-");
        int m = c.get(2) + 1;
        if (m < 10) {
            key.append("0").append(m);
        } else {
            key.append(m);
        }
        key.append("-");
        int d = c.get(5);
        if (d < 10) {
            key.append("0").append(d);
        } else {
            key.append(d);
        }
        key.append(" ");
        int h = c.get(11);
        if (h < 10) {
            key.append("0").append(h);
        } else {
            key.append(h);
        }
        key.append(":");
        int mm = c.get(12);
        if (mm < 10) {
            key.append("0").append(mm);
        } else {
            key.append(mm);
        }
        key.append(":");
        int s = c.get(13);
        if (s < 10) {
            key.append("0").append(s);
        } else {
            key.append(s);
        }
        return key;
    }

    public static List<String> dirs(long timestamp, int granularity, int range, boolean isComplete) {
        java.sql.Date date = new java.sql.Date(System.currentTimeMillis());
        List<String> list = Lists.newArrayList();
        DateTime dateTime = new DateTime(timestamp);
        int delta = range > 0 ? granularity : -granularity;
        int count = Math.abs(range);
        for (int i = 0; i < count; i++) {
            DateTime one = dateTime.plusHours(delta * i);
            if (isComplete) {
                list.add(format(one.getMillis()).toString());
            } else {
                if (one.isBefore(java.sql.Date.valueOf(date.toString()).getTime())) {
                    list.add(format(one.getMillis()).toString());
                }
            }
        }
        return list;
    }

    public static List<String> dirs(long timestamp, int granularity, int range) {

        return dirs(timestamp, granularity, range, false);
    }


    public static int dateRangeCalc(String startDateStr, String endDateStr) {

        DateTime startDate = DateTime.parse(startDateStr);

        DateTime endDate = DateTime.parse(endDateStr);
        if (endDate.getMillis() < startDate.getMillis()) {
            return 0;
        } else {

            return (int) ((endDate.getMillis() - startDate.getMillis()) / (60000 * 60L)) + 24;
        }
    }

    public static List<String> dateRange(String startDateStr, String endDateStr) {

        List<String> ret = new ArrayList<>();
        DateTime startDate = DateTime.parse(startDateStr);

        DateTime endDate = DateTime.parse(endDateStr);
        if (endDate.getMillis() < startDate.getMillis()) {
            return new ArrayList<>();
        } else {

            while (!startDate.isAfter(endDate)) {
                ret.add(startDate.toString("yyyyMMdd"));
                startDate = startDate.plusDays(1);
            }

        }
        return ret;
    }

    public static void main(String[] args) {
        // StringBuilder ret = format(1576571161226L);
        System.out.println((new Date(Timestamp.valueOf("2020-02-10 10:41:03.157").getTime())).getTime());

//        Calendar c = Calendar.getInstance();
//        c.setTimeInMillis(1576571161226L);
//        System.out.println(c.get(13));
//        System.out.println(c.toString());
//        List<String> ret= dateRange("2019-07-17", "2019-07-18");
//        for(String rg:ret) {
//            System.out.println("rg:" + rg);
//        }

//        List<String> dirs = dirs(DateTime.parse("2019-07-17").getMillis(), 1, rg);
//        for (String d : dirs) {
//            System.out.println(d);
//
//        }
    }
}
