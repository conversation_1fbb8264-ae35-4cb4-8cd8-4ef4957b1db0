package com.ctrip.pay.mr.inputformat;

import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.mapreduce.InputSplit;
import org.apache.hadoop.mapreduce.RecordReader;
import org.apache.hadoop.mapreduce.TaskAttemptContext;
import org.apache.hadoop.mapreduce.lib.input.CombineFileSplit;

public class Dummy<PERSON><PERSON>ordReader extends RecordReader<NullWritable,NullWritable> {
    private TaskAttemptContext context;
    private CombineFileSplit s;
    private int idx;
    private boolean used;

    public DummyRecordReader(CombineFileSplit split, TaskAttemptContext context,
                             Integer i) {
        this.context = context;
        this.idx = i;
        this.s = split;
        this.used = true;
    }


    public void initialize(InputSplit split, TaskAttemptContext context) {
        this.context = context;
        this.s = (CombineFileSplit) split;

        // By setting used to true in the c'tor, but false in initialize,
        // we can check that initialize() is always called before use
        // (e.g., in testReinit()).
        this.used = false;
    }

    public boolean nextKeyValue() {
        return false;
    }

    public NullWritable getCurrentKey() {
        return NullWritable.get();
    }

    public NullWritable getCurrentValue() {
        return  NullWritable.get();
    }

    public float getProgress() {
        return used ? 1.0f : 0.0f;
    }

    public void close() {
    }
}

