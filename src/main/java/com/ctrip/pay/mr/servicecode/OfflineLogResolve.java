package com.ctrip.pay.mr.servicecode;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.pay.mr.inputformat.CombinedOrcInputFormat;
import com.ctrip.pay.mr.reducer.CbuWriteMultioutputReducer;
import com.ctrip.pay.mr.utils.DateUtil;
import com.ctrip.pay.mr.utils.FileUtils;
import com.ctrip.pay.mr.utils.MRUtils;
import joptsimple.internal.Strings;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.io.compress.CompressionCodec;
import org.apache.hadoop.io.compress.SnappyCodec;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.LazyOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.hadoop.util.GenericOptionsParser;
import org.apache.hadoop.util.Tool;
import org.apache.hadoop.util.ToolRunner;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;
import org.apache.orc.mapreduce.OrcOutputFormat;
import org.codehaus.jackson.map.ObjectMapper;

import java.io.IOException;
import java.util.*;

public class OfflineLogResolve extends Configured implements Tool {
    public static String schemaPath = "ORCDescription/servicecode_all.properties";

    public static class OfflineLogMapper
            extends Mapper<NullWritable, OrcStruct, Text, OrcValue> {
        int count = 0;
        private Text outKey = new Text();
        private OrcValue outValue = new OrcValue();
        //对应orc表数据结构
        private OrcStruct pair = null;
        private String schema = "";
        //对应hive表字段
        private List<String> fieldList = new ArrayList<>();

        @Override
        protected void setup(Context context) throws IOException, InterruptedException {
            schema = context.getConfiguration().get("orc.mapred.output.schema");
            System.out.println("start to step up schema:" + schema);
            fieldList = MRUtils.initalFields(schemaPath);
            TypeDescription typeDescription = TypeDescription.fromString(schema);
            typeDescription.createRowBatch(100);
            pair = (OrcStruct) OrcStruct.createValue(typeDescription);
        }

        @Override
        protected void map(NullWritable key, OrcStruct value, Context context) throws IOException, InterruptedException {
            String msg = value.getFieldValue(0).toString(); //移除特殊字符

            //解析tag
            Map<String, String> tagsMap = new HashMap<String, String>();

            //解析msg
            String bodyStr = msg;
//            System.out.println("my msg is : "+bodyStr);

            //解析报文体部分
            String payload = "";
            try {
                JSONObject m = JSON.parseObject(msg);

                payload = m.getJSONObject("stored").getString("payload");

                JSON.parseObject(payload);
            } catch (Exception ex) {
                // 非正确json，缺少}
                if (!Strings.isNullOrEmpty(payload)) {
                    payload = payload + "}";
                }
                Calendar calendar = Calendar.getInstance();
                int seconds = calendar.get(Calendar.SECOND);
                if (seconds % 10 == 0) {
                    System.out.println("msgTest is : " + msg);
                }
            }
            JSONObject mm = JSON.parseObject(msg);
            tagsMap.put("servicecode", mm.getString("functionid"));
            tagsMap.put("functionname", mm.getString("functionname"));
            if (mm.getString("paymentAppid") != null)
                tagsMap.put("paymentAppid", mm.getString("paymentAppid"));
            else if (mm.getString("ModuleID") != null)
                tagsMap.put("paymentAppid", mm.getString("ModuleID"));

            tagsMap.put("merchantid", mm.getString("merchantid"));
            if (mm.getString("uid") != null)
                tagsMap.put("uid", mm.getString("uid"));
            else if (mm.getString("CustomerID") != null)
                tagsMap.put("uid", mm.getString("CustomerID"));
            tagsMap.put("orderid", mm.getString("orderid"));
            tagsMap.put("logtype", mm.getString("logtype"));
            tagsMap.put("requestid", mm.getString("requestid"));
            tagsMap.put("messageId", mm.getString("messageId"));

            tagsMap.put("beginTime", mm.getString("createtime"));
            tagsMap.put("endTime", mm.getString("updatetime"));

            //不能用_作为分隔符，因为部分uid就是以_开头的
            outKey.set(tagsMap.get("uid") + MRUtils.col_seperator + tagsMap.get("servicecode") + MRUtils.col_seperator + DateUtil.getDateLong(tagsMap.get("beginTime")));

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < fieldList.size(); i++) {
                String str = fieldList.get(i);
                if (str.equals("tag")) {
                    String jsonStr = new ObjectMapper().writeValueAsString(tagsMap);
                    pair.setFieldValue(i, new Text(jsonStr));
                    sb.append(str + " = " + jsonStr);
                    continue;
                }
                String strVal = tagsMap.get(str);
                if (str.equals("plat")) {
                    strVal = "onlineapi";
                } else if (str.equals("syscode")) {
                    strVal = "01";
                } else if (str.equals("request") && msg.indexOf("Request") > 0) {
                    strVal = payload;
                } else if (str.equals("response") && msg.indexOf("Response") > 0) {
                    strVal = payload;
                } else if (str.equals("requesttime")) {
                    strVal = mm.getString("createtime");
                } else if (str.equals("message")) {
                    strVal = bodyStr;
                } else if (str.equals("message_raw")) {
                    strVal = null;
                }
                sb.append(str + " = " + strVal);
                if (strVal == null) {
                    strVal = "";
                }
                pair.setFieldValue(i, new Text(strVal));
            }
            outValue.value = pair;

            if (count < 3) {
                if (sb.length() < 2000) {
                    System.out.println("mapvalue:" + sb.toString());
                } else {
                    System.out.println("mapvalue:" + sb.toString().substring(1, 2000));
                }
            }
            count++;
            //测试方便只用
            outValue.value = pair;
            context.write(outKey, outValue);
        }
    }


    @Override
    public int run(String[] otherArgs) throws Exception {
        Configuration conf = getConf();
        if (otherArgs.length != 1) {
            System.out.println("argument is wrong,single argument is required");
            return 0;
        }
        String schema = MRUtils.acquireRawSchema(schemaPath);
        conf.set("mapreduce.input.fileinputformat.split.maxsize", "134217728");
        conf.set("mapreduce.task.io.sort.mb", "200");
        conf.setBoolean("mapreduce.map.output.compress", true);
        conf.setClass("mapreduce.map.output.compress.code", SnappyCodec.class, CompressionCodec.class);
        //map内存调大
        conf.set("mapreduce.map.memory.mb", "8096");
        conf.set("mapreduce.map.java.opts", "-Xmx6000m");
        //reduce内存调大
        conf.set("mapreduce.reduce.memory.mb", "8096");
        conf.set("mapreduce.reduce.java.opts", "-Xmx6000m");
        conf.set("mapreduce.reduce.shuffle.memory.limit.percent", "0.15");
//        conf.set("mapreduce.reduce.shuffle.merge.percent", "0.2");
        conf.set("mapreduce.reduce.speculative", "false");

        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(conf, schema);
        OrcConf.MAPRED_SHUFFLE_VALUE_SCHEMA.setString(conf, schema);
        conf.setBoolean(OrcOutputFormat.SKIP_TEMP_DIRECTORY, true);
        conf.set("mapreduce.map.output.value.class", "org.apache.orc.mapred.OrcValue");

        System.out.println("param1:" + otherArgs[0]);
        FileSystem fs = FileSystem.get(conf);
        Job job = Job.getInstance(conf, "pay cbu log etl");
        job.setReduceSpeculativeExecution(false);
        job.setJarByClass(OfflineLogResolve.class);
        job.setMapperClass(OfflineLogMapper.class);
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(OrcValue.class);
        job.setInputFormatClass(CombinedOrcInputFormat.class);
        job.setReducerClass(CbuWriteMultioutputReducer.class);
        job.setOutputKeyClass(NullWritable.class);
        job.setOutputValueClass(OrcStruct.class);

        job.setNumReduceTasks(1000);
        String root = "/user/bifin/pay/servicecode_orc/destination/fx_cat_log_finance-onlinepayment";
        FileStatus[] status = fs.listStatus(new Path(root));
        List<String> partitionLists = new ArrayList<>();
        for (FileStatus s : status) {
            String filePath = s.getPath().toString();
            //必须要包含appid
            if (filePath.matches("(.*)[0-9]+(.*)")) {
                partitionLists.addAll(FileUtils.generateRdInputPrefix(filePath, otherArgs[0], otherArgs[0]));

                // partitionLists.add(FileUtils.generateRdBatchInputPrefix(filePath, otherArgs[0]));
                System.out.println("输入文件路径:" + filePath);

            }
        }
        Collections.shuffle(partitionLists);

        MRUtils.addInputDirs(partitionLists, fs, job);
        //  MRUtils.addInputDirs(partitionLists.subList(1,2), fs, job); //测试时限制个数


        System.out.println("输入文件个数" + partitionLists.size());
        if (partitionLists.size() > 0) {
            System.out.println("输入文件格式" + partitionLists.get(0));

        }
        String outputPath = String.format("/user/biuser/warehouse/etl/Ods_PayDB.db/pay_clog_cbu_union_tmp" + "/dt=%s/", otherArgs[0]);
        FileOutputFormat.setOutputPath(job, new Path(outputPath));
        MultipleOutputs.addNamedOutput(job, "cbulog", OrcOutputFormat.class, NullWritable.class, OrcStruct.class);
        LazyOutputFormat.setOutputFormatClass(job, OrcOutputFormat.class);
        System.out.println("start to delete output:" + outputPath);

        boolean fileDelete = fs.delete(new Path(outputPath), true);
        if (fileDelete) {
            System.out.println("delete output:" + outputPath + " successful");
        } else {
            System.out.println("delete output:" + outputPath + " failed");
        }
        if (otherArgs[otherArgs.length - 1].equals("@")) {
            job.submit();
            return 0;
        } else {
            return job.waitForCompletion(true) ? 0 : 1;
        }
    }


    public static void main(String[] args) throws Exception {
        Configuration conf = new Configuration();
        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
        System.exit(ToolRunner.run(conf, new OfflineLogResolve(), otherArgs));
    }

}
