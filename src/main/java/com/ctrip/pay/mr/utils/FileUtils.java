package com.ctrip.pay.mr.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;

import java.io.IOException;
import java.net.URI;
import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: ymfang
 * @Date: 2019/7/18 11:00
 */
public class FileUtils {

    public static String HIVE_BASE_PATH_OUTPUT_WALLET = "/user/biuser/warehouse/etl/Ods_PayDB.db/pay_clogwalletformat";
    public static String HIVE_BASE_PATH_OUTPUT_ROUTE = "/user/biuser/warehouse/etl/Ods_PayDB.db/fpayclogrouterformat";
    public static String HIVE_BASE_PATH_OUTPUT = "/user/biuser/warehouse/etl/Ods_PayDB.db/fpayclogformat";
    public static String HIVE_BASE_PATH_INPUT = "/user/bifin/pay/servicecode/destination/pal_cbu_log_created";

    public static String HIVE_BASE_PATH_INPUT_WALLET = "/user/bifin/wallet/appid/destination_compare/fnc_log_created";
    public static String HIVE_BASE_PATH_INPUT_ROUTE = "/user/bifin/pay/router/destination/fnc_log_back_100003993_created";
    public static String HIVE_BASE_PATH_INPUT_LOG = "/user/bifin/pay/pay_log_raw/fnc_log_tis_callback_destination/fnc_log_tis_callback";
    public static String HIVE_BASE_PATH_INPUT_OLD = "/user/bifin/pay/servicecode_old/destination/pal_cbulognet_created";
    public static String HIVE_BASE_PATH_INPUT_TMP = "/user/bifin/pay/servicecode_temp/";
    public static String HIVE_BASE_PATH_INPUT_ONLINE = "/user/bifin/pay/servicecode_online/destination/fx_cat_log_finance-onlinepayment";
    public static String HIVE_BASE_PATH_INPUT_ONLINE_OY = "/user/bifin/pay/servicecode_online/destination_oy/fx_cat_log_finance-onlinepayment";
    private static String whitelistName = "ORCDescription/blacklist.properties";

    public static String getOutputDir(String servicecode, String dt) {
        return String.format(HIVE_BASE_PATH_OUTPUT + "/servicecode=%s/dt=%s", servicecode, dt);
    }

    public static String getRouteOutputDir(String servicecode, String dt) {
        return String.format(HIVE_BASE_PATH_OUTPUT_ROUTE + "/appid=%s/dt=%s", servicecode, dt);
    }



    public static String getWalletOutputDir(String appid, String dt) {
        return String.format(HIVE_BASE_PATH_OUTPUT_WALLET + "/appid=%s/dt=%s", appid, dt);
    }

    public static List<String> generateServicecodeInput(String servicecode, String startDate) {
        String[] pathList=servicecode.split(",");
        if(pathList.length==1) {
            return generateServicecodeInput(servicecode, startDate, startDate);
        }else{
            List<String> ret = new ArrayList<>();
            for(String path:pathList){
                ret.addAll(generateServicecodeInput(path, startDate, startDate));
            }
            return ret;
        }
    }

    public static List<String> generateServicecodeInput(String servicecode, String startDate, String endDate) {

        if (!StringUtils.isNotEmpty(servicecode) || !StringUtils.isNotEmpty(startDate) || !StringUtils.isNotEmpty(endDate)) {
            return new ArrayList<String>();
        }
        int rang = DateUtil.dateRangeCalc(startDate, endDate);//小时数

        Date date = Date.valueOf(startDate);
        List<String> dirs = DateUtil.dirs(date.getTime(), 1, rang, true); //两个月
        List<String> path = Lists.newArrayList();
        for (String replace : dirs) {
            String inPutPath = String.format(HIVE_BASE_PATH_INPUT + "/%s/hourly/%s/", new Object[]{servicecode, replace});
            path.add(inPutPath);
            inPutPath = String.format(HIVE_BASE_PATH_INPUT_OLD + "/%s/hourly/%s/", new Object[]{servicecode, replace});
            path.add(inPutPath);
        }
        return path;
    }

    public static List<String> generateRouteInput(String appid, String startDate, String endDate) {

        return generateRouteInput(HIVE_BASE_PATH_INPUT_ROUTE, appid, startDate, endDate);
    }

    public static List<String> generateRouteInput(String root,String appid, String startDate, String endDate) {

        if (!StringUtils.isNotEmpty(startDate) || !StringUtils.isNotEmpty(endDate)) {
            return new ArrayList<String>();
        }
        int rang = DateUtil.dateRangeCalc(startDate, endDate);//小时数

        Date date = Date.valueOf(startDate);
        List<String> dirs = DateUtil.dirs(date.getTime(), 1, rang, true); //两个月
        List<String> path = Lists.newArrayList();
        for (String replace : dirs) {
            String inPutPath = String.format(root + "/%s/hourly/%s/", new Object[]{appid, replace});
            path.add(inPutPath);
        }
        return path;
    }

    public static List<String> generateRdInputPrefix(String rootPath, String startDate, String endDate) {

        if (!StringUtils.isNotEmpty(startDate) || !StringUtils.isNotEmpty(endDate)) {
            return new ArrayList<String>();
        }
        int rang = DateUtil.dateRangeCalc(startDate, endDate);//小时数

        Date date = Date.valueOf(startDate);
        List<String> dirs = DateUtil.dirs(date.getTime(), 1, rang, true); //两个月
        List<String> path = Lists.newArrayList();
        for (String replace : dirs) {
            String inPutPath = String.format(rootPath + "/hourly/%s/", new Object[]{ replace});
            path.add(inPutPath);
        }
        return path;
    }

    public static List<String> generateRdInputPrefix2(String rootPath, String startDate, String endDate) {

        if (!StringUtils.isNotEmpty(startDate) || !StringUtils.isNotEmpty(endDate)) {
            return new ArrayList<String>();
        }
        int rang = DateUtil.dateRangeCalc(startDate, endDate);//小时数
        List<String> path = Lists.newArrayList();
        Date date = Date.valueOf(startDate);
        for (int i = 0; i < rang; i++) {
            if(i<10){
                String inPutPath = String.format(rootPath +"/d=" +date +"/h=%s/", new Object[]{ "0"+i});
                path.add(inPutPath);
            }else {
                String inPutPath = String.format(rootPath +"/d=" +date +"/h=%s/", new Object[]{ i});
                path.add(inPutPath);
            }
        }
        return path;
    }

    public static String generateRdBatchInputPrefix(String rootPath, String startDate) {

        if (StringUtils.isEmpty(startDate) ) {
            return "";
        }
        return String.format(rootPath + "/d=%s/", new Object[]{startDate});
    }

    /**
     * online日志
     * @param servicecode
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<String> generateOnlineInput(String servicecode,String startDate, String endDate) {

        if (!StringUtils.isNotEmpty(startDate) || !StringUtils.isNotEmpty(endDate)) {
            return new ArrayList<String>();
        }
        int rang = DateUtil.dateRangeCalc(startDate, endDate);//小时数

        Date date = Date.valueOf(startDate);
        List<String> dirs = DateUtil.dirs(date.getTime(), 1, rang,true); //两个月
        List<String> path = Lists.newArrayList();
        for (String replace : dirs) {
            String inPutPath = String.format(HIVE_BASE_PATH_INPUT_ONLINE + "/%s/hourly/%s/", new Object[]{servicecode, replace});
            path.add(inPutPath);
            System.out.println("input:"+inPutPath);
            inPutPath = String.format(HIVE_BASE_PATH_INPUT_ONLINE_OY + "/%s/hourly/%s/", new Object[]{servicecode, replace});
            System.out.println("input:"+inPutPath);
            path.add(inPutPath);
        }
        return path;
    }


    /***
     * wallet log
     * @param appid
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<String> generateWalletInput(String appid,String startDate, String endDate) {

        if (!StringUtils.isNotEmpty(startDate) || !StringUtils.isNotEmpty(endDate)) {
            return new ArrayList<String>();
        }
        int rang = DateUtil.dateRangeCalc(startDate, endDate);//小时数

        Date date = Date.valueOf(startDate);
        List<String> dirs = DateUtil.dirs(date.getTime(), 1, rang,true); //两个月
        List<String> path = Lists.newArrayList();
        for (String replace : dirs) {
            String inPutPath = String.format(HIVE_BASE_PATH_INPUT_WALLET + "/%s/hourly/%s/", new Object[]{appid, replace});
            path.add(inPutPath);
            System.out.println("input:"+inPutPath);
        }
        return path;
    }

    /**
     * sample:/user/bifin/pay/servicecode/destination/pal_cbu_log_created/31000102/hourly/2019/07/17/21
     * 获取hdfs上所有的servicecode
     *
     * @return
     * @throws IOException
     */
    public static List<String> getBlackListServiceCode() throws IOException {

        List<String> blackList = MRUtils.getBlacklist(whitelistName);

        List<String> scList = new ArrayList<>();
        // 创建Configuration对象
        Configuration conf = new Configuration();
        FileSystem fs = FileSystem.get(URI.create(HIVE_BASE_PATH_INPUT), conf);

        FileStatus[] status = fs.listStatus(new Path(HIVE_BASE_PATH_INPUT));

        for (FileStatus s : status) {
            String filePath = s.getPath().toString();
            String pattern = "pal_cbu_log_created/(\\d+)";
            Pattern r = Pattern.compile(pattern);
            String scName = "";
            // 现在创建 matcher 对象
            Matcher m = r.matcher(filePath);
            if (m.find()) {
                scName = m.group(1);
            }
            if (!blackList.contains(scName)) {
                scList.add(scName);
            }
        }
        return scList;

    }

    public static String regexpMatch(String str, String match) {
    //"version":"A","layer":"L14545","expCode":"191022_PAY_nqhv2"
        //t":"(.*?)"
        String pattern = match+"\":\"(.*?)\"";
        // 创建 Pattern 对象
        Pattern r = Pattern.compile(pattern);

        // 现在创建 matcher 对象
        Matcher m = r.matcher(str);
        if (m.find()) {
            return m.group(1);
        }

        return "";

    }

    public static List<String> traveLogPathList(String root, FileSystem fs,String date,String appId) throws IOException {
        FileStatus[] status = fs.listStatus(new Path(root));
        //获取topic信息
        List<String> partitionLists = new ArrayList<>();

        for (FileStatus s : status) {
            String filePath = s.getPath().toString();
            if (filePath.contains("destination")) {
                // 统计topic列表
                FileStatus[] dest = fs.listStatus(new Path(filePath));

                for (FileStatus dst : dest) {
                    String subFilePath = dst.getPath().toString();
                    //必须要包含appid
                    if (subFilePath.contains("destination") && subFilePath.matches("(.*)[0-9]+(.*)")) {
                        if(StringUtils.isEmpty(appId)) {
                            partitionLists.addAll(FileUtils.generateRdInputPrefix(subFilePath, date, date));
                        }else{
                            if(subFilePath.contains(appId)){
                                partitionLists.addAll(FileUtils.generateRdInputPrefix(subFilePath, date, date));
                            }
                        }
                    }
                }
            }
        }

        for(String path:partitionLists){
            System.out.println("输入文件路径:" + path);
        }
        return partitionLists;
    }


    public static String getMaskFileName(int num ){

        int file = 10000 + num;
        return "0" + String.valueOf(file).substring(1);

    }


    public static void main(String[] args) {
//        List<String> dir = generateWalletInput("100006883", "2019-12-18", "2019-12-18");
//        for (String str : dir) {
//            System.out.println(str);
//        }

//        String str = "32001025210034627903\",\"effectTime\":\"2019-11-29 15:45:46\",\"version\":\"A\",\"layer\":\"L14545\",\"expCode\":\"191022_PAY_nqhv2\"";
//        System.out.println(regexpMatch(str,"version"));
//        System.out.println(regexpMatch(str,"expCode"));
//        System.out.println(str.contains("expCode"));

        //  System.out.println(getRouteOutputDir("12121212","2019-08-19"));

//        String filePath = "hdfs://ns/user/bifin/pay/servicecode/destination/pal_cbu_log_created/90200401";
//        String pattern = "pal_cbu_log_created/(\\d+)";
//        Pattern r = Pattern.compile(pattern);
//        String scName = "";
//        // 现在创建 matcher 对象
//        Matcher m = r.matcher(filePath);
//        if(m.find()) {
//           System.out.println(m.group(1));
//        }

//        String str9 = "/user/bifin/pay/topic/data/fnc_log_client_route";
//        List<String> strings = generateRdInputPrefix2(str9, "2023-05-28", "2023-05-28");
//        System.out.println(getMaskFileName(72));

        List<String> strings = FileUtils.generateRdInputPrefix("/user/bifin/pay/servicecode_orc/destination/fx_cat_log_finance-onlinepayment", "2025-06-30", "2025-06-30");
        strings.forEach(System.out::println);
    }
}
