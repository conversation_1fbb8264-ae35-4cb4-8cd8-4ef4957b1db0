package com.ctrip.pay.mr.servicecode;

import com.ctrip.pay.mr.reducer.AbtestOrcWriteReducer;
import com.ctrip.pay.mr.servicecode.serialize.MessageParser;
import com.ctrip.pay.mr.servicecode.serialize.TagParser;
import com.ctrip.pay.mr.utils.FileUtils;
import com.ctrip.pay.mr.utils.MRUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileContext;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.util.GenericOptionsParser;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.util.Tool;
import org.apache.hadoop.util.ToolRunner;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcMap;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;
import org.apache.orc.mapreduce.OrcOutputFormat;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SCAbtestResolve extends Configured implements Tool {
    public static String schemaPath = "ORCDescription/abtest.properties";
    public static class SCAbtestMapper
            extends Mapper<LongWritable, Text, Text, OrcValue> {
        int count = 0;
        private Text outKey = new Text();
        private OrcValue outValue = new OrcValue();
        //对应hive表字段
        private List<String> fieldList = new ArrayList<>();
        private Map<String, String> fieldValue = Maps.newHashMap();
        //对应orc表数据结构
        private OrcStruct pair = null;
        private String schema = "";


        @Override
        protected void setup(Context context) throws IOException, InterruptedException {

            fieldList = MRUtils.initalFields(schemaPath);
            schema = MRUtils.acquireRawSchema(schemaPath);
            pair = (OrcStruct) OrcStruct.createValue(TypeDescription.fromString(schema));
        }

        @Override
        protected void map(LongWritable key, Text value, Context context) throws IOException, InterruptedException {
            String msg = value.toString().trim();
            if (msg.length() < 20  ) { //异常行
                return;
            }
            //解析tag
            Map<String, String> tagsMap = TagParser.parseTags(msg);
            //找不到orderid或者uid、bustype的
            String uid = tagsMap.get("uid");
            if (!(tagsMap.containsKey("orderid") || tagsMap.containsKey("uid")  || tagsMap.containsKey("bustype"))) {
                return;
            }
            //paymentcommon
            if(!Strings.isNullOrEmpty(tagsMap.get("calleetype")) && !tagsMap.get("calleetype").equalsIgnoreCase("di.data.abtest")
                    ){
                return;
            }

            //解析msg
            String bodyStr = MessageParser.parseBody(msg);

            tagsMap.put("expcode",FileUtils.regexpMatch(bodyStr,"expCode"));
            tagsMap.put("version",FileUtils.regexpMatch(bodyStr,"version"));
            //防止uid为空
            if( StringUtils.isEmpty(uid)){
                return;
            }
            outKey.set(uid);
            OrcConf.MAPRED_SHUFFLE_VALUE_SCHEMA.setString(context.getConfiguration(), schema);
            OrcConf.MAPRED_OUTPUT_SCHEMA.setString(context.getConfiguration(), schema);

            //赋值uid、orderid、bustype,syscode
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < fieldList.size(); i++) {
                String str = fieldList.get(i);
                if (str.equals("tag")) {
                    OrcMap orcMap = (OrcMap) pair.getFieldValue(i);
                    if (tagsMap.size() > 0) {
                        for (String elem : tagsMap.keySet()) {
                            orcMap.put(new Text(elem), new Text(tagsMap.get(elem)));
                        }
                    }
                    pair.setFieldValue(i, orcMap);
                    continue;
                }

                String strVal = tagsMap.get(str);

                sb.append(str + " = " + strVal);
                if (strVal == null) {
                    strVal = "";
                }
                pair.setFieldValue(i, new Text(strVal));

            }
            outValue.value = pair;

            if (count < 3) {
                if (sb.length() < 2000) {
                    System.out.println("orginvalue:" + bodyStr.toString());

                    System.out.println("mapvalue:" + sb.toString());
                } else {
                    System.out.println("orginvalue:" + bodyStr.toString().substring(1, 2000));

                    System.out.println("mapvalue:" + sb.toString().substring(1, 2000));
                }
            }
            count++;
            //测试方便只用
          //  if (count > 1000) return;
            fieldValue.clear(); //清空
            context.write(outKey, outValue);
        }
    }


    @Override
    public int run(String[] otherArgs) throws Exception {
        Configuration conf = getConf();
        if (otherArgs.length != 3) {
            System.out.println("argument is wrong,two argument is needed");
            return 0;
        }
        String schema = MRUtils.acquireRawSchema(schemaPath);
        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(conf, schema);
        conf.set("orc.mapred.output.schema", schema);
        conf.set("orc.mapred.map.output.value.schema", schema);

        System.out.println("param1:" + otherArgs[0]);
        System.out.println("param2:" + otherArgs[1]);
        System.out.println("param3:" + otherArgs[2]);
        FileSystem fs = FileSystem.get(conf);
        Job job = Job.getInstance(conf, otherArgs[0] + "_" + otherArgs[1] + " jiexi");
        job.setJarByClass(SCAbtestResolve.class);
        job.setMapperClass(SCAbtestMapper.class);
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(OrcValue.class);
        job.getConfiguration().set("orc.mapred.value.type", "OrcValue");

        job.setReducerClass(AbtestOrcWriteReducer.class);
        job.setOutputFormatClass(OrcOutputFormat.class);

        job.setNumReduceTasks(300);
        List<String> partitionLists = FileUtils.generateServicecodeInput(otherArgs[0], otherArgs[1]);
        System.out.println("输入文件数量:" + partitionLists.size());
        MRUtils.addInputDirs(partitionLists, fs, job);
        OrcOutputFormat.setOutputPath(job, new Path(otherArgs[2]));

        System.out.println("start to delete output:" + otherArgs[2]);

        boolean fileDelete = fs.delete(new Path(otherArgs[2]), true);
        if (fileDelete) {
            System.out.println("delete output:" + otherArgs[2] + " successful");
        } else {
            System.out.println("delete output:" + otherArgs[2] + " failed");
        }
        if (otherArgs[otherArgs.length - 1].equals("@")) {
            job.submit();
            return 0;
        } else {
            return job.waitForCompletion(true) ? 0 : 1;
        }
    }


    public static void main(String[] args) throws Exception {
        Configuration conf = new Configuration();
        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
        int ret=ToolRunner.run(conf, new SCAbtestResolve(), otherArgs);
        if(ret==0) {
            FileSystem fs = FileSystem.get(conf);
            Path des = new Path("/user/biuser/warehouse/etl/DW_PayDB.db/dwd_pay_abtest_d/d=" + otherArgs[1]);
            if (fs.exists(des)) {
                fs.delete(des, true);
            }
            FileContext.getFileContext().util().copy(new Path(otherArgs[2]), des);
            System.exit(ret);
        }
        System.exit(ret);
    }

}
