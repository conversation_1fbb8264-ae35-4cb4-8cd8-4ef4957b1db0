package com.ctrip.pay.mr.servicecode;

import com.alibaba.fastjson.JSON;
import com.ctrip.pay.mr.servicecode.serialize.MessageParser;
import com.ctrip.pay.mr.servicecode.serialize.TagParser;
import com.ctrip.pay.mr.servicecode.serialize.TraceModel;
import com.ctrip.pay.mr.utils.DateUtil;
import com.ctrip.pay.mr.utils.FileUtils;
import com.ctrip.pay.mr.utils.MRUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.util.GenericOptionsParser;
import org.apache.hadoop.util.Tool;
import org.apache.hadoop.util.ToolRunner;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcMap;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;
import org.apache.orc.mapreduce.OrcOutputFormat;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class SC31004001Resolve extends Configured implements Tool {
    public static String schemaPath = "ORCDescription/servicecode.properties";
    public static class SC31004001Mapper
            extends Mapper<LongWritable, Text, Text, OrcValue> {
        int count = 0;
        private Text outKey = new Text();
        private OrcValue outValue = new OrcValue();
        //对应hive表字段
        private List<String> fieldList = new ArrayList<>();
        private Map<String, String> fieldValue = Maps.newHashMap();
        //对应orc表数据结构
        private OrcStruct pair = null;
        private String schema = "";


        @Override
        protected void setup(Context context) throws IOException, InterruptedException {

            fieldList = MRUtils.initalFields(schemaPath);
            schema = MRUtils.acquireRawSchema(schemaPath);
            pair = (OrcStruct) OrcStruct.createValue(TypeDescription.fromString(schema));
        }

        @Override
        protected void map(LongWritable key, Text value, Context context) throws IOException, InterruptedException {
            String msg = value.toString().trim();
            if (msg.length() < 20) { //异常行
                return;
            }
            //解析tag
            Map<String, String> tagsMap = TagParser.parseTags(msg);
            //找不到orderid或者uid、bustype的
            if (!(tagsMap.containsKey("orderid") || tagsMap.containsKey("uid") || tagsMap.containsKey("bustype"))) {
                return;
            }

            //若logtype不是paymentinfo，则不保存
            if(!Strings.isNullOrEmpty(tagsMap.get("logtype"))
                    && !tagsMap.get("logtype").equalsIgnoreCase("paymentinfo")
                    && !tagsMap.get("logtype").equalsIgnoreCase("paymentinfosoa")
                    ){
                return;
            }

            //解析msg
            String bodyStr = MessageParser.parseBody(msg);
            TraceModel model = null;
            try {
                model = JSON.parseObject(bodyStr, TraceModel.class);
            } catch (Exception e) {


            }
            if (model == null) {
                model = new TraceModel();
            }
            //计算出amount、plat 和resultcode
            if (!tagsMap.containsKey("amount")) {
                tagsMap.put("amount", model.getAmount("31004001") + "");
            }
            if (!tagsMap.containsKey("plat")) {
                tagsMap.put("plat", model.getPlat() + "");
            }
            tagsMap.put("resultcode", model.getResultcode() + "");
            String uid = "";
            if (tagsMap.containsKey("uid")) {
                uid = tagsMap.get("uid");
            }
            outKey.set(uid);
            OrcConf.MAPRED_SHUFFLE_VALUE_SCHEMA.setString(context.getConfiguration(), schema);
            OrcConf.MAPRED_OUTPUT_SCHEMA.setString(context.getConfiguration(), schema);

            //赋值uid、orderid、bustype,syscode
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < fieldList.size(); i++) {
                String str = fieldList.get(i);
                if (str.equals("tag")) {
                    OrcMap orcMap = (OrcMap) pair.getFieldValue(i);
                    if (tagsMap.size() > 0) {
                        for (String elem : tagsMap.keySet()) {
                            orcMap.put(new Text(elem), new Text(tagsMap.get(elem)));
                        }
                    }
                    pair.setFieldValue(i, orcMap);
                    continue;
                }

                String strVal = tagsMap.get(str);
                if (str.equals("request")) {
                    strVal = JSON.toJSONString(model.getRequest());
                } else if (str.equals("response")) {
                    strVal = JSON.toJSONString(model.getResponse());
                } else if (str.equals("requesttime")) {
                    if(model.getBeginTime()==null){ //做兼容
                        strVal = DateUtil.dateFormat.format(new Date());
                    }else {
                        strVal = DateUtil.dateFormat.format(model.getBeginTime());
                    }
                } else if (str.equals("message")) {
                    strVal = bodyStr;
                } else if (str.equals("message_raw")) {
                    strVal = msg;
                }
                sb.append(str + " = " + strVal);
                if (strVal == null) {
                    strVal = "";
                }
                pair.setFieldValue(i, new Text(strVal));

            }
            outValue.value = pair;

            if (count < 3) {
                if (sb.length() < 2000) {
                    System.out.println("mapvalue:" + sb.toString());
                } else {
                    System.out.println("mapvalue:" + sb.toString().substring(1, 2000));
                }
            }
            count++;
            //测试方便只用
          //  if (count > 1000) return;
            fieldValue.clear(); //清空
            context.write(outKey, outValue);
        }
    }


    @Override
    public int run(String[] otherArgs) throws Exception {
        Configuration conf = getConf();
        if (otherArgs.length != 2) {
            System.out.println("argument is wrong,two argument is needed");
            return 0;
        }
        String schema = MRUtils.acquireRawSchema(schemaPath);
        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(conf, schema);
        conf.set("orc.mapred.output.schema", schema);
        conf.set("orc.mapred.map.output.value.schema", schema);

        System.out.println("param1:" + otherArgs[0]);
        System.out.println("param2:" + otherArgs[1]);
        FileSystem fs = FileSystem.get(conf);
        Job job = Job.getInstance(conf, otherArgs[0] + "_" + otherArgs[1] + " jiexi");
        job.setJarByClass(SC31004001Resolve.class);
        job.setMapperClass(SC31004001Mapper.class);
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(OrcValue.class);
        job.getConfiguration().set("orc.mapred.value.type", "OrcValue");

        job.setReducerClass(OrcWriteReducer.class);
        job.setOutputFormatClass(OrcOutputFormat.class);

        job.setNumReduceTasks(300);
        List<String> partitionLists = FileUtils.generateServicecodeInput(otherArgs[0], otherArgs[1]);
        System.out.println("输入文件数量:" + partitionLists.size());
        MRUtils.addInputDirs(partitionLists, fs, job);
        String outputPath = FileUtils.getOutputDir(otherArgs[0], otherArgs[1]);
        OrcOutputFormat.setOutputPath(job, new Path(outputPath));

        System.out.println("start to delete output:" + outputPath);

        boolean fileDelete = fs.delete(new Path(outputPath), true);
        if (fileDelete) {
            System.out.println("delete output:" + outputPath + " successful");
        } else {
            System.out.println("delete output:" + outputPath + " failed");
        }
        if (otherArgs[otherArgs.length - 1].equals("@")) {
            job.submit();
            return 0;
        } else {
            return job.waitForCompletion(true) ? 0 : 1;
        }
    }


    public static void main(String[] args) throws Exception {
        Configuration conf = new Configuration();
        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
        System.exit(ToolRunner.run(conf, new SC31004001Resolve(), otherArgs));
    }

}
