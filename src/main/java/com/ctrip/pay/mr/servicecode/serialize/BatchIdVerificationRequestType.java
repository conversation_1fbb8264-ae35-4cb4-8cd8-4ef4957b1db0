package com.ctrip.pay.mr.servicecode.serialize;

import java.util.List;

public class BatchIdVerificationRequestType {
    private String Extension ;
    private List<IdVerificationDto> BatchIdVerificationDtos ;

    private int Nonce ;

    private String Sign ;

    private long Timestamp ;

    private String Token ;

    public String getExtension() {
        return Extension;
    }

    public void setExtension(String extension) {
        Extension = extension;
    }

    public List<IdVerificationDto> getBatchIdVerificationDtos() {
        return BatchIdVerificationDtos;
    }

    public void setBatchIdVerificationDtos(List<IdVerificationDto> batchIdVerificationDtos) {
        BatchIdVerificationDtos = batchIdVerificationDtos;
    }

    public int getNonce() {
        return Nonce;
    }

    public void setNonce(int nonce) {
        Nonce = nonce;
    }

    public String getSign() {
        return Sign;
    }

    public void setSign(String sign) {
        Sign = sign;
    }

    public long getTimestamp() {
        return Timestamp;
    }

    public void setTimestamp(long timestamp) {
        Timestamp = timestamp;
    }

    public String getToken() {
        return Token;
    }

    public void setToken(String token) {
        Token = token;
    }
}
