package com.ctrip.pay.mr.servicecode;


import com.ctrip.pay.mr.servicecode.serialize.MessageParser;
import com.ctrip.pay.mr.servicecode.serialize.TagParser;
import com.ctrip.pay.mr.utils.FileUtils;
import com.ctrip.pay.mr.utils.MRUtils;
import com.google.common.base.Strings;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.util.GenericOptionsParser;
import org.apache.hadoop.util.Tool;
import org.apache.hadoop.util.ToolRunner;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcMap;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;
import org.apache.orc.mapreduce.OrcOutputFormat;

import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class APPID100011182Resolve extends Configured implements Tool {
    public static String schemaPath = "ORCDescription/appid.properties";
    public static class APPID100011182Mapper
            extends Mapper<LongWritable, Text, Text, OrcValue> {
        int count = 0;
        private Text outKey = new Text();
        private OrcValue outValue = new OrcValue();
        //对应hive表字段
        private List<String> fieldList = new ArrayList<>();

        //对应orc表数据结构
        private OrcStruct pair = null;
        private String schema = "";

        @Override
        protected void setup(Context context) throws IOException, InterruptedException {
            fieldList = MRUtils.initalFields(schemaPath);
            schema = MRUtils.acquireRawSchema(schemaPath);
            pair = (OrcStruct) OrcStruct.createValue(TypeDescription.fromString(schema));
        }

        @Override
        protected void map(LongWritable key, Text value, Context context) throws IOException, InterruptedException {
            String msg = value.toString().trim();
            if (msg.length() < 20) { //异常行
                return;
            }

            //解析tag
//            Map<String,String> tags= new HashMap<>();

            Map<String,String> tags = TagParser.parseTags(msg);
            String payload = MessageParser.parseBody(msg);
            String request = "";
            String response = "";

            try {
                if(payload==null || payload.length() == 0){
                    return;
                }
            } catch (Exception e) {
                return;
            }

            if(!tags.containsKey("servicecode")){
                return;
            }
            if( ! tags.containsKey("title")
                    ||  Strings.isNullOrEmpty(tags.get("title"))
                    || !(tags.get("title").equalsIgnoreCase("ServiceTrace")
                    || tags.get("title").equalsIgnoreCase("SoaTrace")
                    || tags.get("title").equalsIgnoreCase("ComponentTrace")) ){
                return;
            }

            //extract request json
            Pattern p = Pattern.compile("request:(.*?)response");
            Matcher mct = p.matcher(payload);

            if(mct.find()){
                request = mct.group(1);
            }
            //extract response json
            p = Pattern.compile("response:(.*?)$");
            mct = p.matcher(payload);

            if(mct.find()){
                response = mct.group(1);
            }

            //extract requesttime
            p = Pattern.compile("requesttime:(.*?)responsetime");
            mct = p.matcher(msg.toLowerCase());

            if(mct.find()){
                tags.put("requesttime",mct.group(1));
            }
            // title 为 ComponentTrace、SoaTrace, 以originuid替换uid
            if(tags.get("title").equalsIgnoreCase("SoaTrace")
                    || tags.get("title").equalsIgnoreCase("ComponentTrace")){
                if(!Strings.isNullOrEmpty(tags.get("originuid"))) {
                    tags.put("uid", tags.get("originuid"));
                }
            }

            String uid = "";
            if (tags.containsKey("uid")) {
                uid = tags.get("uid");
            }
            if(Strings.isNullOrEmpty(uid)){
                uid=!Strings.isNullOrEmpty(tags.get("app_uid"))?tags.get("app_uid"):"";
            }

            outKey.set(uid);
            OrcConf.MAPRED_SHUFFLE_VALUE_SCHEMA.setString(context.getConfiguration(), schema);
            OrcConf.MAPRED_OUTPUT_SCHEMA.setString(context.getConfiguration(), schema);

            //赋值uid、orderid、bustype,syscode
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < fieldList.size(); i++) {
                String str = fieldList.get(i);
                if (str.equals("tag")) {
                    OrcMap orcMap = (OrcMap) pair.getFieldValue(i);
                    if (tags.size() > 0) {
                        for (String elem : tags.keySet()) {
                            try{
                                orcMap.put(new Text(elem), new Text(tags.get(elem)));
                            }catch (Exception e){
                                System.out.println(e.toString());
                                continue;
                            }
                        }
                    }
                    pair.setFieldValue(i, orcMap);
                    continue;
                }

                String strVal = tags.get(str);
                if (str.equals("request") && msg.indexOf("request")>0) {
                    strVal = request;
                } else if (str.equals("response") && msg.indexOf("response")>0) {
                    strVal = response;
                } else if (str.equals("message")) {
                    strVal = msg;
                }
                sb.append(str + " = " + strVal);
                if (strVal == null) {
                    strVal = "";
                }
                pair.setFieldValue(i, new Text(strVal));
            }

            outValue.value = pair;
            context.write(outKey, outValue);
        }
    }

    @Override
    public int run(String[] otherArgs) throws Exception {

        Configuration conf = getConf();
        if (otherArgs.length != 2) {
            System.out.println("argument is wrong,two argument is needed");
            return 0;
        }
        String schema = MRUtils.acquireRawSchema(schemaPath);
        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(conf, schema);
        conf.set("orc.mapred.output.schema", schema);
        conf.set("orc.mapred.map.output.value.schema", schema);

        System.out.println("param1:" + otherArgs[0]);
        System.out.println("param2:" + otherArgs[1]);
        FileSystem fs = FileSystem.get(conf);
        Job job = Job.getInstance(conf, otherArgs[0] + "_" + otherArgs[1] + " jiexi");
        job.setJarByClass(APPID100011182Resolve.class);
        job.setMapperClass(APPID100011182Resolve.APPID100011182Mapper.class);
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(OrcValue.class);
        job.getConfiguration().set("orc.mapred.value.type", "OrcValue");

        job.setReducerClass(OrcWriteReducer.class);
        job.setOutputFormatClass(OrcOutputFormat.class);

        job.setNumReduceTasks(300);
        List<String> partitionLists = FileUtils.generateWalletInput(otherArgs[0], otherArgs[1],otherArgs[1]);
        System.out.println("输入文件数量:" + partitionLists.size());
        MRUtils.addInputDirs(partitionLists, fs, job);
        String outputPath = FileUtils.getWalletOutputDir(otherArgs[0], otherArgs[1]);
        OrcOutputFormat.setOutputPath(job, new Path(outputPath));

        System.out.println("start to delete output:" + outputPath);

        boolean fileDelete = fs.delete(new Path(outputPath), true);
        if (fileDelete) {
            System.out.println("delete output:" + outputPath + " successful");
        } else {
            System.out.println("delete output:" + outputPath + " failed");
        }
        if (otherArgs[otherArgs.length - 1].equals("@")) {
            job.submit();
            return 0;
        } else {
            return job.waitForCompletion(true) ? 0 : 1;
        }
    }

    public static void main(String[] args) throws Exception {
        Configuration conf = new Configuration();
        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
        System.exit(ToolRunner.run(conf, new APPID100011182Resolve(), otherArgs));
    }
}
