package com.ctrip.pay.mr.reducer;

import com.alibaba.fastjson.JSON;
import com.ctrip.pay.mr.servicecode.serialize.MessageParser;
import com.ctrip.pay.mr.servicecode.serialize.TagParser;
import com.ctrip.pay.mr.servicecode.serialize.TraceModel;
import com.ctrip.pay.mr.utils.DateUtil;
import com.ctrip.pay.mr.utils.MRUtils;
import com.google.common.collect.Maps;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcMap;
import org.apache.orc.mapred.OrcStruct;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: ymfang
 * @Date: 2019/7/18 14:52
 */
public class OrcWriteMultioutputReducerSide
        extends Reducer<Text, Text, NullWritable, OrcStruct> {
    private int count = 0;
    private String schema = "";
    private MultipleOutputs multipleOutputs;
    //对应hive表字段
    private List<String> fieldList = new ArrayList<>();
    private Map<String, String> fieldValue = Maps.newHashMap();
    //对应orc表数据结构
    private OrcStruct pair = null;
    public static String schemaPath = "ORCDescription/servicecode.properties";

    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        multipleOutputs=new MultipleOutputs(context);
        fieldList = MRUtils.initalFields(schemaPath);
        schema = MRUtils.acquireRawSchema(schemaPath);
        pair = (OrcStruct) OrcStruct.createValue(TypeDescription.fromString(schema));
    }

    @Override
    public void cleanup(Context context) throws IOException, InterruptedException
    {
        multipleOutputs.close();

    }

    @Override
    protected void reduce(Text key, Iterable<Text> values, Context context) throws IOException, InterruptedException {

        String[] keyStr=key.toString().split("_");


        for (Text itm : values) {
            if ((count++) < 3) {
                System.out.println("InnerReducer:" + count + ": uid=" + key+"  value:" +itm.toString());
            }

            //解析msg
            String bodyStr = MessageParser.parseBody(itm.toString());
            Map<String, String> tagsMap = TagParser.parseTags(bodyStr);

            TraceModel model = null;
            try {
                model = JSON.parseObject(bodyStr, TraceModel.class);
            } catch (Exception e) {


            }
            if (model == null) {
                model = new TraceModel();
            }
            //计算出plat 和resultcode
            if (!tagsMap.containsKey("plat")) {
                tagsMap.put("plat", model.getPlat() + "");
            }
            tagsMap.put("resultcode", model.getResultcode() + "");


            //赋值uid、orderid、bustype,syscode
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < fieldList.size(); i++) {
                String str = fieldList.get(i);
                if (str.equals("tag")) {
                    OrcMap orcMap = (OrcMap) pair.getFieldValue(i);
                    if (tagsMap.size() > 0) {
                        for (String elem : tagsMap.keySet()) {
                            orcMap.put(new Text(elem), new Text(tagsMap.get(elem)));
                        }
                    }
                    pair.setFieldValue(i, orcMap);
                    continue;
                }

                String strVal = tagsMap.get(str);
                if (str.equals("request")) {
                    strVal = JSON.toJSONString(model.getRequest());
                } else if (str.equals("response")) {
                    strVal = JSON.toJSONString(model.getResponse());
                } else if (str.equals("requesttime")) {
                    strVal = DateUtil.dateFormat.format(model.getBeginTime());
                } else if (str.equals("message")) {
                    strVal = bodyStr;
                } else if (str.equals("message_raw")) {
                    strVal = bodyStr;
                }
                sb.append(str + " = " + strVal);
                if (strVal == null) {
                    strVal = "";
                }
                pair.setFieldValue(i, new Text(strVal));

            }
            multipleOutputs.write(keyStr[1],NullWritable.get(), pair,keyStr[0]+"/"+keyStr[1]+"/");
        }

    }
}