package com.ctrip.pay.mr.servicecode.serialize;

import com.alibaba.fastjson.JSON;

public class SC31002403RequestParser {
    private String BusinessEType;
    private String Platform;
    private String PayEType;
    private String RequestID;
    private String UseEType;
    private String SubUseEType;
    private String SubPayType;

    private OrderInfo orderInfo;
    private ThirdPartyInfo thirdPartyInfo;
    private CreditCardInfo creditCardInfo;

    public String getBusinessEType() {
        return BusinessEType;
    }

    public void setBusinessEType(String businessEType) {
        BusinessEType = businessEType;
    }

    public String getPlatform() {
        return Platform;
    }

    public void setPlatform(String platform) {
        Platform = platform;
    }

    public String getPayEType() {
        return PayEType;
    }

    public void setPayEType(String payEType) {
        PayEType = payEType;
    }

    public String getRequestID() {
        return RequestID;
    }

    public void setRequestID(String requestID) {
        RequestID = requestID;
    }

    public String getUseEType() {
        return UseEType;
    }

    public void setUseEType(String useEType) {
        UseEType = useEType;
    }

    public String getSubUseEType() {
        return SubUseEType;
    }

    public void setSubUseEType(String subUseEType) {
        SubUseEType = subUseEType;
    }

    public String getSubPayType() {
        return SubPayType;
    }

    public void setSubPayType(String subPayType) {
        SubPayType = subPayType;
    }

    public OrderInfo getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(OrderInfo orderInfo) {
        this.orderInfo = orderInfo;
    }

    public ThirdPartyInfo getThirdPartyInfo() {
        return thirdPartyInfo;
    }

    public void setThirdPartyInfo(ThirdPartyInfo thirdPartyInfo) {
        this.thirdPartyInfo = thirdPartyInfo;
    }

    public CreditCardInfo getCreditCardInfo() {
        return creditCardInfo;
    }

    public void setCreditCardInfo(CreditCardInfo creditCardInfo) {
        this.creditCardInfo = creditCardInfo;
    }

    public class OrderInfo{
        private String OrderId;

        public String getOrderId() {
            return OrderId;
        }

        public void setOrderId(String orderId) {
            OrderId = orderId;
        }
    }
    public class ThirdPartyInfo{
        private String PaymentWayID;
        private String ThirdTypeId;
        private String ThirdSubTypeId;
        private String ThirdTypeCode;
        private double ThirdAmount;

        public String getPaymentWayID() {
            return PaymentWayID;
        }

        public void setPaymentWayID(String paymentWayID) {
            PaymentWayID = paymentWayID;
        }

        public String getThirdTypeId() {
            return ThirdTypeId;
        }

        public void setThirdTypeId(String thirdTypeId) {
            ThirdTypeId = thirdTypeId;
        }

        public String getThirdSubTypeId() {
            return ThirdSubTypeId;
        }

        public void setThirdSubTypeId(String thirdSubTypeId) {
            ThirdSubTypeId = thirdSubTypeId;
        }

        public String getThirdTypeCode() {
            return ThirdTypeCode;
        }

        public void setThirdTypeCode(String thirdTypeCode) {
            ThirdTypeCode = thirdTypeCode;
        }

        public double getThirdAmount() {
            return ThirdAmount;
        }

        public void setThirdAmount(double thirdAmount) {
            ThirdAmount = thirdAmount;
        }
    }

    public class CreditCardInfo{
        private String PaymentWayID;
//        private String bindcid;
//        private String opttype;
//        private String status;
        private String CardAmount;
        private AddCardInfo AddCardInfo;

        public String getPaymentWayID() {
            return PaymentWayID;
        }

        public void setPaymentWayID(String paymentWayID) {
            PaymentWayID = paymentWayID;
        }

        public String getCardAmount() {
            return CardAmount;
        }

        public void setCardAmount(String cardAmount) {
            CardAmount = cardAmount;
        }

        public CreditCardInfo.AddCardInfo getAddCardInfo() {
            return AddCardInfo;
        }

        public void setAddCardInfo(CreditCardInfo.AddCardInfo addCardInfo) {
            AddCardInfo = addCardInfo;
        }

        public class AddCardInfo{
            private String CardTypeID;
            private String CardTypeMain;
            private String IsLast4Pay;
            private String CardTypeCategory;

            public String getCardTypeID() {
                return CardTypeID;
            }

            public void setCardTypeID(String cardTypeID) {
                CardTypeID = cardTypeID;
            }

            public String getCardTypeMain() {
                return CardTypeMain;
            }

            public void setCardTypeMain(String cardTypeMain) {
                CardTypeMain = cardTypeMain;
            }

            public String getIsLast4Pay() {
                return IsLast4Pay;
            }

            public void setIsLast4Pay(String isLast4Pay) {
                IsLast4Pay = isLast4Pay;
            }

            public String getCardTypeCategory() {
                return CardTypeCategory;
            }

            public void setCardTypeCategory(String cardTypeCategory) {
                CardTypeCategory = cardTypeCategory;
            }
        }
    }

    public static void main(String[] args){
        String msg = "\"[[syscode=17,uid=_FB2979349385,logtype=paymentinfo,servicecode=31002403,orderid=***********,serviceversion=701.0,guid=78777e7f-32f3-4d18-86b9-8b964d7d15e5,serverIp=************,title=applePaySubmitV2,calleetype=applePaySubmitV2,bustype=7504]]{\\\"serviceName\\\":\\\"applePaySubmitV2\\\",\\\"requestHead\\\":{\\\"appId\\\":\\\"37\\\",\\\"authToken\\\":\\\"30F6E285079DB26B2626BCE2ED04C5DC5B8841D9C4FA9441B9DF8C7FEA427B78\\\",\\\"clientId\\\":\\\"37001122310021374709\\\",\\\"clientToken\\\":\\\"e941F2srqT56eyyC3Zx8\\\",\\\"clientVersion\\\":701.0,\\\"exSourceID\\\":\\\"\\\",\\\"extentionList\\\":[{\\\"key\\\":\\\"locale\\\",\\\"value\\\":\\\"en-SG\\\"},{\\\"key\\\":\\\"pageId\\\",\\\"value\\\":\\\"10320607461\\\"},{\\\"key\\\":\\\"vid\\\",\\\"value\\\":\\\"661C19EB27424CD89884132394969585\\\"},{\\\"key\\\":\\\"sid\\\",\\\"value\\\":\\\"69\\\"},{\\\"key\\\":\\\"pvid\\\",\\\"value\\\":\\\"1460\\\"},{\\\"key\\\":\\\"x-via\\\",\\\"value\\\":\\\"akamai\\\"},{\\\"key\\\":\\\"x-client-ip\\\",\\\"value\\\":\\\"**************\\\"}],\\\"language\\\":\\\"09\\\",\\\"messageNumber\\\":\\\"22768884387343\\\",\\\"remoteIPAddress\\\":\\\"**************\\\",\\\"serializeCode\\\":\\\"PB\\\",\\\"serviceCode\\\":\\\"31002403\\\",\\\"sourceId\\\":\\\"8890\\\",\\\"systemCode\\\":\\\"17\\\",\\\"userId\\\":\\\"_FB2979349385\\\"},\\\"request\\\":{\\\"UserSourceType\\\":0,\\\"PassPort\\\":{\\\"TouchPayInfo\\\":{\\\"DeviceInfo\\\":{\\\"DeviceModel\\\":\\\"\\\",\\\"DeviceGUID\\\":\\\"\\\",\\\"VendorId\\\":\\\"\\\",\\\"IMEI\\\":\\\"\\\",\\\"WiFiMac\\\":\\\"\\\",\\\"KeyGUID\\\":\\\"\\\"},\\\"Token\\\":\\\"\\\",\\\"KeyGUID\\\":\\\"\\\"},\\\"Password\\\":\\\"\\\"},\\\"SDiscountInfo\\\":{\\\"DiscountKey\\\":\\\"\\\",\\\"DiscountTitle\\\":\\\"\\\",\\\"Extend\\\":\\\"\\\",\\\"DiscountType\\\":0,\\\"DiscountAmount\\\":0},\\\"LastPayForTime\\\":\\\"\\\",\\\"ForeignCardCharge\\\":0,\\\"Platform\\\":1,\\\"RequestID\\\":\\\"zSovoOVZSoyCRt3YhF+\\\",\\\"CreditDeductionInfo\\\":{\\\"ChargeMode\\\":0,\\\"CreditDefuctionBrandId\\\":\\\"\\\",\\\"CreditDefuctionPaymentWayId\\\":\\\"\\\",\\\"CreditDefucionPayAmout\\\":\\\"0\\\",\\\"CreditDefuctionChannelId\\\":\\\"\\\",\\\"CreditDefuctionAmount\\\":0,\\\"Password\\\":\\\"\\\"},\\\"PayRestrict\\\":{\\\"DiscountBlackList\\\":\\\"\\\",\\\"DiscountIDList\\\":\\\"\\\",\\\"SENameList\\\":\\\"\\\",\\\"SubTypeList\\\":0,\\\"PayTypeList\\\":0,\\\"RestrictBit\\\":0,\\\"DefaultPayType\\\":0},\\\"ActivityKey\\\":\\\"\\\",\\\"ForStatistics\\\":\\\"1|6\\\",\\\"OrderLatestAvailableTime\\\":\\\"\\\",\\\"OpenId\\\":\\\"\\\",\\\"BusinessEType\\\":7504,\\\"SubPayType\\\":0,\\\"ScenarioInfo\\\":{\\\"Category\\\":0},\\\"MaxActivityCount\\\":0,\\\"SeqID\\\":\\\"\\\",\\\"OPBitMap\\\":\\\"\\\",\\\"CreditCardInfo\\\":{\\\"BindId\\\":\\\"\\\",\\\"ChargeMode\\\":0,\\\"BrandType\\\":\\\"\\\",\\\"BankCountry\\\":\\\"\\\",\\\"UpdateCardInfo\\\":{\\\"CardNumber\\\":\\\"\\\",\\\"Email\\\":\\\"\\\",\\\"BusinessNumber\\\":\\\"\\\",\\\"ExpireDate\\\":\\\"\\\",\\\"Mobilephone\\\":\\\"\\\",\\\"IsLast4Pay\\\":false,\\\"Birthday\\\":\\\"\\\",\\\"CardTypeCategory\\\":0,\\\"CVV2\\\":\\\"\\\",\\\"VerifyCode\\\":\\\"\\\",\\\"CardPwd\\\":\\\"\\\",\\\"CardInfoId\\\":0,\\\"ReferenceId\\\":\\\"\\\",\\\"CardTypeID\\\":0,\\\"Last4Code\\\":\\\"\\\",\\\"IDCardNumber\\\":\\\"\\\",\\\"CardNumFirstAndLast\\\":\\\"\\\",\\\"CardHolder\\\":\\\"\\\",\\\"CardNoRefID\\\":0,\\\"IDCardType\\\":0,\\\"CardTypeMain\\\":0},\\\"AddCardInfo\\\":{\\\"CardNumber\\\":\\\"\\\",\\\"IdCardNumber\\\":\\\"\\\",\\\"Email\\\":\\\"\\\",\\\"BusinessNumber\\\":\\\"\\\",\\\"ExpireDate\\\":\\\"\\\",\\\"IsLast4Pay\\\":false,\\\"Mobilephone\\\":\\\"\\\",\\\"Birthday\\\":\\\"\\\",\\\"CardTypeCategory\\\":0,\\\"IdCardType\\\":0,\\\"CVV2\\\":\\\"\\\",\\\"VerifyCode\\\":\\\"\\\",\\\"CardPwd\\\":\\\"\\\",\\\"ReferenceID\\\":\\\"\\\",\\\"CardTypeID\\\":0,\\\"CardHolder\\\":\\\"\\\",\\\"CardTypeMain\\\":0},\\\"BrandId\\\":\\\"\\\",\\\"ChannelId\\\":\\\"\\\",\\\"BillAddress\\\":\\\"\\\",\\\"CheckCardInfo\\\":{\\\"CardNumber\\\":\\\"\\\",\\\"Email\\\":\\\"\\\",\\\"BusinessNumber\\\":\\\"\\\",\\\"ExpireDate\\\":\\\"\\\",\\\"Mobilephone\\\":\\\"\\\",\\\"IsLast4Pay\\\":false,\\\"Birthday\\\":\\\"\\\",\\\"CardTypeCategory\\\":0,\\\"CVV2\\\":\\\"\\\",\\\"VerifyCode\\\":\\\"\\\",\\\"CardPwd\\\":\\\"\\\",\\\"CardInfoId\\\":0,\\\"ReferenceId\\\":\\\"\\\",\\\"CardTypeID\\\":0,\\\"Last4Code\\\":\\\"\\\",\\\"IDCardNumber\\\":\\\"\\\",\\\"CardNumFirstAndLast\\\":\\\"\\\",\\\"CardHolder\\\":\\\"\\\",\\\"CardNoRefID\\\":0,\\\"IDCardType\\\":0,\\\"CardTypeMain\\\":0},\\\"Exchange\\\":\\\"\\\",\\\"BillingAddress\\\":{\\\"StreetName\\\":\\\"\\\",\\\"Email\\\":\\\"\\\",\\\"Address\\\":\\\"\\\",\\\"IdCardNo\\\":\\\"\\\",\\\"City\\\":\\\"\\\",\\\"IdCardType\\\":\\\"\\\",\\\"Province\\\":\\\"\\\",\\\"PostNo\\\":\\\"\\\",\\\"CardBank\\\":\\\"\\\",\\\"CardBankCountry\\\":\\\"\\\",\\\"Country\\\":\\\"\\\",\\\"StreetNumber\\\":\\\"\\\",\\\"CardHolder\\\":\\\"\\\",\\\"CountryCode\\\":\\\"\\\",\\\"ContractPhoneNum\\\":\\\"\\\"},\\\"CardAmount\\\":\\\"0\\\",\\\"CardStatusBitMap\\\":0,\\\"CardStatusMap\\\":0,\\\"InstallmentNumber\\\":0,\\\"PaymentWayID\\\":\\\"\\\",\\\"DCCInfo\\\":{\\\"DCCAmount\\\":\\\"0\\\",\\\"DCCCurrency\\\":\\\"\\\",\\\"DCCExchange\\\":\\\"\\\"},\\\"OperateType\\\":0},\\\"UserInfoSaveFlag\\\":0,\\\"OperateType\\\":1,\\\"PayEType\\\":4,\\\"TravelTicketInfo\\\":{\\\"TicketAmount\\\":\\\"0\\\",\\\"PaymentWayID\\\":\\\"\\\",\\\"Password\\\":\\\"\\\"},\\\"SubUseEType\\\":0,\\\"MyAccoutInfo\\\":{\\\"IdNum\\\":\\\"\\\",\\\"Name\\\":\\\"\\\",\\\"IdType\\\":0},\\\"ExtendParam\\\":\\\"[{\\\\\\\"tripLegFrom\\\\\\\":\\\\\\\"TPE\\\\\\\",\\\\\\\"firstName\\\\\\\":\\\\\\\"HSIN HSUAN\\\\\\\",\\\\\\\"ticketReservation\\\\\\\":\\\\\\\"***********\\\\\\\",\\\\\\\"ticketNumber\\\\\\\":\\\\\\\"***********\\\\\\\",\\\\\\\"surname\\\\\\\":\\\\\\\"WANG\\\\\\\",\\\\\\\"tripLegDate\\\\\\\":\\\\\\\"2019-08-09\\\\\\\",\\\\\\\"flightNumber\\\\\\\":\\\\\\\"TR899,TR874\\\\\\\",\\\\\\\"tripLegTo\\\\\\\":\\\\\\\"SIN\\\\\\\"}]\\\",\\\"UseEType\\\":1,\\\"Attach\\\":\\\"\\\",\\\"OPAdapterBitMap\\\":4,\\\"OrderInfo\\\":{\\\"PaymentNotify\\\":\\\"\\\",\\\"OrderIDExtend\\\":***********,\\\"AutoApplyBill\\\":true,\\\"Currency\\\":\\\"SGD\\\",\\\"RecallType\\\":\\\"\\\",\\\"OrderAmount\\\":\\\"398.34\\\",\\\"OrderID\\\":1850602708,\\\"OrderDesc\\\":\\\"Trip.com booking\\\",\\\"ExternalNo\\\":\\\"9c708261-7090-41f4-9f1e-3376d77ed792\\\",\\\"10\\\":[\\\"0\\\"]},\\\"ForeignCardFee\\\":\\\"0\\\",\\\"ThirdPartyInfo\\\":{\\\"ThirdPayCardNum\\\":\\\"\\\",\\\"ChargeMode\\\":67,\\\"BrandType\\\":\\\"2\\\",\\\"PromotionId\\\":\\\"\\\",\\\"CouponId\\\":\\\"\\\",\\\"ThirdTypeId\\\":0,\\\"ThirdTypeCode\\\":\\\"\\\",\\\"BrandId\\\":\\\"APPLE\\\",\\\"ChannelId\\\":\\\"676\\\",\\\"Exchange\\\":\\\"5.103750\\\",\\\"ThirdFee\\\":0,\\\"ExtendJson\\\":\\\"\\\",\\\"Extend\\\":\\\"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\\\",\\\"CollectionId\\\":\\\"TRD.ADYEN.SGD.APM\\\",\\\"PaymentWayID\\\":\\\"APPLE\\\",\\\"ThirdSubTypeId\\\":0,\\\"ThirdAmount\\\":\\\"398.34\\\"},\\\"RiskVerifyCode\\\":\\\"\\\",\\\"ServiceVersion\\\":701,\\\"CoordinateItemList\\\":[{\\\"CoordinateEType\\\":4,\\\"Latitude\\\":\\\"0\\\",\\\"Longitude\\\":\\\"0\\\"}],\\\"StreamControlBitmap\\\":0},\\\"response\\\":{\\\"ThresholdTime\\\":0,\\\"OrderIDExtend\\\":***********,\\\"ResultMessage\\\":\\\"Payment successful.\\\",\\\"Result\\\":0,\\\"BillNo\\\":\\\"2684594314\\\",\\\"CardInfoID\\\":751532959,\\\"ThirdPartyInfo\\\":{\\\"Type\\\":0,\\\"Sigurature\\\":\\\"\\\",\\\"ReferenceNo\\\":\\\"TransNo:2505170029\\\"},\\\"RiskCode\\\":\\\"0\\\",\\\"IdentityVerify\\\":0,\\\"OrderLeftTime\\\":0,\\\"SeqId\\\":\\\"1908071558423912pzw\\\",\\\"NativeWechatUrl\\\":\\\"\\\",\\\"SubCode\\\":\\\"cbu_100\\\",\\\"DebugMessage\\\":\\\"{\\\\\\\"result\\\\\\\":true,\\\\\\\"code\\\\\\\":\\\\\\\"FNC4219900\\\\\\\",\\\\\\\"message\\\\\\\":\\\\\\\"成功\\\\\\\"}\\\"},\\\"beginTime\\\":\\\"2019-08-07 23:58:42.375\\\",\\\"endTime\\\":\\\"2019-08-07 23:58:49.646\\\",\\\"timeSpan\\\":7271,\\\"appId\\\":\\\"100017656\\\"}\"";
        String bodyStr = MessageParser.parseBody(msg);
        TraceModel model = null;
        try {
            model = JSON.parseObject(bodyStr, TraceModel.class);
            SC31002403RequestParser ret = JSON.parseObject(model.getRequest().toString(), SC31002403RequestParser.class);
            System.out.println(ret.getThirdPartyInfo().getPaymentWayID());
        } catch (Exception e) {

        }

    }
}
