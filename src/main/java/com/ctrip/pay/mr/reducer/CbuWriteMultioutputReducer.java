package com.ctrip.pay.mr.reducer;

import com.ctrip.pay.mr.utils.FileUtils;
import com.ctrip.pay.mr.utils.MRUtils;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import org.apache.hadoop.mapreduce.TaskID;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;

import java.io.IOException;

/**
 * @Author: ymfang
 * @Date: 2019/7/18 14:52
 */
public class CbuWriteMultioutputReducer
        extends Reducer<Text, OrcValue, NullWritable, OrcStruct> {
    private int count = 0;
    private String schema = "";
    private MultipleOutputs<NullWritable, OrcStruct> multipleOutputs;
    private FileSystem fs;

    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        schema = context.getConfiguration().get("orc.mapred.output.schema");
        multipleOutputs = new MultipleOutputs(context);
        TypeDescription typeDescription = TypeDescription.fromString(schema);
        typeDescription.createRowBatch(100);
        fs = FileSystem.get(context.getConfiguration());

    }

    @Override
    public void cleanup(Context context) throws IOException, InterruptedException {
        if (multipleOutputs != null) {
            multipleOutputs.close();
        }

    }

    @Override
    protected void reduce(Text key, Iterable<OrcValue> values, Context context) throws IOException, InterruptedException {

        Splitter splitter = Splitter.on(MRUtils.col_seperator).trimResults();
        String[] fields = Lists.newArrayList(splitter.split(key.toString())).toArray(new String[]{});
        if (fields == null || fields.length != 3) {
            return;
        }

        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(context.getConfiguration(), schema);

        for (OrcValue itm : values) {
            if ((count++) < 3) {
                String path = context.getConfiguration().get("cbu.path") + "app_bucket=" + fields[1] + "/";
                TaskID taskId = context.getTaskAttemptID().getTaskID();
                String fileName = "-r-00" + FileUtils.getMaskFileName(taskId.getId()) + ".orc";

                String filePath = path + fileName;
                System.out.println("InnerReducer:" + count + ": uid=" + key + "  value:" + itm.toString() + "filePath:" + filePath);

            }
            try {
                multipleOutputs.write("cbulog", NullWritable.get(), (OrcStruct) itm.value, "servicecode=" + fields[1] + "/");
            } catch (Exception e) {

                String path = context.getConfiguration().get("cbu.path") + "servicecode=" + fields[1] + "/";
                TaskID taskId = context.getTaskAttemptID().getTaskID();
                String fileName = "-r-" + FileUtils.getMaskFileName(taskId.getId()) + ".orc";
                String filePath = path + fileName;
                boolean fileDelete = fs.delete(new Path(filePath), true);
                if (fileDelete) {
                    System.out.println("delete output:" + filePath + " successful");
                } else {
                    System.out.println("delete output:" + filePath + " failed");
                }

            }
        }


    }
}