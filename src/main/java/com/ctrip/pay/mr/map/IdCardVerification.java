package com.ctrip.pay.mr.map;

import com.ctrip.pay.mr.inputformat.CombinedOrcInputFormat;
import com.ctrip.pay.mr.reducer.IDVWriteReducer;
import com.ctrip.pay.mr.utils.HiveUtils;
import com.ctrip.pay.mr.utils.MRUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.io.compress.CompressionCodec;
import org.apache.hadoop.io.compress.SnappyCodec;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.LazyOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.hadoop.util.GenericOptionsParser;
import org.apache.hadoop.util.Tool;
import org.apache.hadoop.util.ToolRunner;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;
import org.apache.orc.mapreduce.OrcInputFormat;
import org.apache.orc.mapreduce.OrcOutputFormat;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class IdCardVerification extends Configured implements Tool {
    static String INPUT_SCHEMA = "struct<uid:string,name:string,id_no:string>";
    static String OUTPUT_SCHEMA = "struct<uid:string,idName:string,idNumber:string,verifyCode:string,verifyMsg:string,verifyServiceType:string>";
    public static class IdCardMapper extends Mapper<NullWritable, OrcStruct, Text, OrcValue>
    {
        private Text outKey = new Text();
        private OrcValue outValue = new OrcValue();
        @Override
        protected void setup(Context context) throws IOException, InterruptedException {

        }

        @Override
        protected void map(NullWritable key, OrcStruct value, Context context) throws IOException, InterruptedException {
            String idno=value.getFieldValue(2).toString();
            //不能用_作为分隔符，因为部分uid就是以_开头的
            outKey.set(idno);
            outValue.value = value;
            context.write(outKey, outValue);
        }
    }


    @Override
    public int run(String[] otherArgs) throws Exception {
        Configuration conf = getConf();
        //tablename,reducecount,outputname
        if (otherArgs.length != 3) {
            System.out.println("argument is wrong,three argument is required");
            return 0;
        }

        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(conf, OUTPUT_SCHEMA);
        OrcConf.MAPRED_SHUFFLE_VALUE_SCHEMA.setString(conf, INPUT_SCHEMA);
        conf.setBoolean(OrcOutputFormat.SKIP_TEMP_DIRECTORY, true);
        conf.set("mapreduce.map.output.value.class", "org.apache.orc.mapred.OrcValue");

        System.out.println("param1:" + otherArgs[0]);
        FileSystem fs = FileSystem.get(conf);
        Job job = Job.getInstance(conf, "id verification");
        job.setJarByClass(IdCardVerification.class);
        job.setMapperClass(IdCardVerification.IdCardMapper.class);
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(OrcValue.class);
        job.setInputFormatClass(OrcInputFormat.class);
        job.setReducerClass(IDVWriteReducer.class);
        job.setOutputKeyClass(NullWritable.class);
        job.setOutputValueClass(OrcStruct.class);
        job.setGroupingComparatorClass(GroupingStragy.GroupingComparator.class);
        job.setNumReduceTasks(Integer.parseInt(otherArgs[1]));
        String root = "/user/biuser/warehouse/etl/Tmp_PayDB.db/"+ otherArgs[0];
        FileInputFormat.addInputPath(job, new Path(root));

        String outputPath ="/user/bifin/jar/"+ otherArgs[2];
        FileOutputFormat.setOutputPath(job, new Path(outputPath));
        job.setOutputFormatClass(OrcOutputFormat.class);
        System.out.println("start to delete output:" + outputPath);

        boolean fileDelete = fs.delete(new Path(outputPath), true);
        if (fileDelete) {
            System.out.println("delete output:" + outputPath + " successful");
        } else {
            System.out.println("delete output:" + outputPath + " failed");
        }
        if (otherArgs[otherArgs.length - 1].equals("@")) {
            job.submit();
            return 0;
        } else {
            return job.waitForCompletion(true) ? 0 : 1;
        }
    }

    public static void main(String[] args) throws Exception {
        Configuration conf = new Configuration();
        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
        ToolRunner.run(conf, new IdCardVerification(), otherArgs);
        String outputPath ="/user/bifin/jar/"+ otherArgs[2];
        System.exit(HiveUtils.loadDataIntoTable("tmp_paydb", otherArgs[2], OUTPUT_SCHEMA, outputPath));
    }
}
