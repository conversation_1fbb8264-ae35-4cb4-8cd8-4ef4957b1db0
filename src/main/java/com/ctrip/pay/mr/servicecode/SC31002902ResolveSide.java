package com.ctrip.pay.mr.servicecode;

import com.ctrip.pay.mr.reducer.OrcWriteMultioutputReducerSide;
import com.ctrip.pay.mr.servicecode.serialize.MessageParser;
import com.ctrip.pay.mr.servicecode.serialize.TagParser;
import com.ctrip.pay.mr.utils.DateUtil;
import com.ctrip.pay.mr.utils.FileUtils;
import com.ctrip.pay.mr.utils.MRUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.mapreduce.lib.input.FileSplit;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.hadoop.util.GenericOptionsParser;
import org.apache.hadoop.util.Tool;
import org.apache.hadoop.util.ToolRunner;
import org.apache.orc.OrcConf;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapreduce.OrcOutputFormat;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public class SC31002902ResolveSide extends Configured implements Tool {
    public static String schemaPath = "ORCDescription/servicecode.properties";

    public static class SC31002902Mapper
            extends Mapper<LongWritable, Text, Text, Text> {
        int count = 0;
        private Text outKey = new Text();


        @Override
        protected void setup(Context context) throws IOException, InterruptedException {

        }

        @Override
        protected void map(LongWritable key, Text value, Context context) throws IOException, InterruptedException {
            String msg = value.toString().trim();
            if (msg.length() < 20) { //异常行
                return;
            }

            //解析tag
            Map<String, String> tagsMap = TagParser.parseTags(msg);
            //找不到orderid或者uid的
            if (!(tagsMap.containsKey("orderid") || tagsMap.containsKey("uid"))) {
                return;
            }


            //解析msg
            String bodyStr = MessageParser.parseBody(msg);


            String filename = ((FileSplit) context.getInputSplit()).getPath().toString();
            String[] days = filename.split("hourly/")[1].split("/");
            String day = (new StringBuilder(days[0]).append(days[1]).append(days[2])).toString();
            String uid = tagsMap.get("servicecode") + "_" + day;
            outKey.set(uid);

            //赋值uid、orderid、bustype,syscode

            if (count < 3) {
                if (bodyStr.length() < 2000) {
                    System.out.println("mapvalue:" + bodyStr.toString());
                } else {
                    System.out.println("mapvalue:" + bodyStr.toString().substring(1, 2000));
                }
            }
            count++;
            //测试方便只用
            if (count > 1000) return;
            context.write(outKey, new Text(bodyStr));
        }
    }


    @Override
    public int run(String[] otherArgs) throws Exception {
        Configuration conf = getConf();
        if (otherArgs.length != 4) {
            System.out.println("argument is wrong,three argument is needed");
            return 0;
        }
        String schema = MRUtils.acquireRawSchema(schemaPath);
        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(conf, schema);
        conf.set("orc.mapred.output.schema", schema);
        conf.set("orc.mapred.map.output.value.schema", schema);

        System.out.println("sccode:" + otherArgs[0]);
        System.out.println("beg:" + otherArgs[1]);
        System.out.println("end:" + otherArgs[2]);
        System.out.println("output:" + otherArgs[3]);
        String outputBasePath = otherArgs[3].trim();

        FileSystem fs = FileSystem.get(conf);
        Job job = Job.getInstance(conf, otherArgs[0] + "_" + otherArgs[1] + " jiexi");
        job.setJarByClass(SC31002902ResolveSide.class);
        job.setMapperClass(SC31002902Mapper.class);
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(Text.class);
        job.getConfiguration().set("orc.mapred.value.type", "OrcValue");

        job.setReducerClass(OrcWriteMultioutputReducerSide.class);
        job.setOutputKeyClass(NullWritable.class);
        job.setOutputValueClass(OrcStruct.class);
        fs.delete(new Path(outputBasePath), true);
        /**ORC outputformat**/
        FileOutputFormat.setOutputPath(job, new Path(outputBasePath));

        job.setNumReduceTasks(3);
        List<String> partitionLists = FileUtils.generateServicecodeInput(otherArgs[0], otherArgs[1]);
        MRUtils.addInputDirs(partitionLists, fs, job);

        for (String str : DateUtil.dateRange(otherArgs[1], otherArgs[2])) {
            MultipleOutputs.addNamedOutput(job, str, OrcOutputFormat.class, NullWritable.class, OrcStruct.class);
        }
//        LazyOutputFormat.setOutputFormatClass(job, OrcOutputFormat.class);
        OrcOutputFormat.setCompressOutput(job, false);

        if (otherArgs[otherArgs.length - 1].equals("@")) {
            job.submit();
            return 0;
        } else {
            return job.waitForCompletion(true) ? 0 : 1;
        }
    }


    public static void main(String[] args) throws Exception {
        Configuration conf = new Configuration();
        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
        System.exit(ToolRunner.run(conf, new SC31002902ResolveSide(), otherArgs));
    }

}
