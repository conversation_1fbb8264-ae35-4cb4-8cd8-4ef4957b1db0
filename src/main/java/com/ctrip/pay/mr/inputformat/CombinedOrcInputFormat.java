package com.ctrip.pay.mr.inputformat;

import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.WritableComparable;
import org.apache.hadoop.mapreduce.InputSplit;
import org.apache.hadoop.mapreduce.RecordReader;
import org.apache.hadoop.mapreduce.TaskAttemptContext;
import org.apache.hadoop.mapreduce.lib.input.CombineFileInputFormat;
import org.apache.hadoop.mapreduce.lib.input.CombineFileRecordReaderWrapper;
import org.apache.hadoop.mapreduce.lib.input.CombineFileSplit;
import org.apache.orc.mapreduce.OrcInputFormat;

import java.io.IOException;

public class CombinedOrcInputFormat<V extends WritableComparable> extends CombineFileInputFormat<NullWritable, V> {

    @Override
    public RecordReader<NullWritable, V> createRecordReader(InputSplit inputSplit,
                                                            TaskAttemptContext taskAttemptContext) {
        try {
            return new CombinedSmallFileRecordReader((CombineFileSplit) inputSplit, taskAttemptContext, OrcRecordReaderWrapper.class);
        } catch (Exception exp) {
            RecordReader dummyRecordReader = new DummyRecordReader((CombineFileSplit) inputSplit, taskAttemptContext, 0);
            return dummyRecordReader;
        }

    }

    private static class OrcRecordReaderWrapper<V extends WritableComparable>
            extends CombineFileRecordReaderWrapper<NullWritable, V> {
        public OrcRecordReaderWrapper(CombineFileSplit split,
                                      TaskAttemptContext context, Integer idx) throws IOException, InterruptedException {
            super(new OrcInputFormat(), split, context, idx);
        }
    }

}
