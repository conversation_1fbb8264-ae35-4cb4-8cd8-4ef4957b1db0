package com.ctrip.pay.mr.utils;

import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.JSONPathException;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PathUtil {
    //    private static final Logger log = Logger.getLogger(PathUtil.class);
    static Pattern p = Pattern.compile("\t|\r|\n|\0|\u0000");
    public static final String col_seperator = "\0";
    public static final String col_replace = "{col_replace}";
    private static ConcurrentMap<String, JSONPath> pathCache = new ConcurrentHashMap<String, JSONPath>(128, 0.75f, 1);

    public static String getObject(Object json, String path, String def) {
        if (null == path) return def;
        String[] paths = path.split("\\|");
        for (String v : paths) {
            try {
                JSONPath jsonpath = compile(v);
                Object ret = jsonpath.eval(json);
                if (null != ret) {
                    String s = String.valueOf(ret);
                    Matcher m = p.matcher(s.trim().replaceAll(col_seperator, col_replace));
                    return m.replaceAll("");
                }
            } catch (Exception e) {
//                log.info("unsupported:"+path+",json:"+json);
//                return def;
            }
        }
        return def;
    }

    /**
     * 移除特殊字符
     * @param ret
     * @return
     */
    public static String preHandleObject(String ret) {
        if (null != ret) {
            String s = String.valueOf(ret);
            Matcher m = p.matcher(s.trim().replaceAll(col_seperator, col_replace));
            return m.replaceAll("");
        }
        return "";
    }


    public static JSONPath compile(String path) {
        if (path == null) {
            throw new JSONPathException("jsonpath can not be null");
        }
        JSONPath jsonpath = pathCache.get(path);
        if (jsonpath == null) {
            jsonpath = new JSONPath(path);
            pathCache.putIfAbsent(path, jsonpath);
            jsonpath = pathCache.get(path);
        }
        return jsonpath;
    }


    public static void main(String[] args) {
        String bodyStr = "\":null}\"";
        if(bodyStr.endsWith("\"")){
         //   bodyStr = bodyStr.substring(1, bodyStr.length() - 1);
        }
        System.out.println(bodyStr);
    }
}
