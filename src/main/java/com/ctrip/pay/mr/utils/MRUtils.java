package com.ctrip.pay.mr.utils;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.io.WritableComparable;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcMap;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MRUtils {

    public static final String SEPARATOR_FIELD = new String(new char[]{1}); //"\001"
    public static final String col_seperator="\0";

    public static void addInputDirs(List<String> partitionLists, FileSystem fs, Job job) throws IOException {

        for (int i = 0; i < partitionLists.size(); i++) {
            Path p = new Path(partitionLists.get(i));
            if (fs.exists(p)) {
                FileInputFormat.addInputPath(job, new Path(partitionLists.get(i)));
            } else {
                //System.out.println("未找到路径:" + partitionLists.get(i));
            }
        }
    }

    public static InputStream getInputStreamByFileName(String fileName) {
        InputStream in = MRUtils.class.getClassLoader().getResourceAsStream(fileName);
        return in;
    }

    public static List<String> getBlacklist(String shema) throws IOException {
        InputStream inputStream = getInputStreamByFileName(shema);
        List<String> retSchema = new ArrayList<String>();
        try {
            retSchema = IOUtils.readLines(inputStream, "utf8");
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
        return retSchema;
    }

    public static List<String> initalFields(String shema) throws IOException {
        InputStream inputStream = getInputStreamByFileName(shema);
        List<String> retSchema = new ArrayList<String>();
        try {
            retSchema = IOUtils.readLines(inputStream, "utf8");
        } finally {
            IOUtils.closeQuietly(inputStream);
        }

        List<String> ret= new ArrayList<String>();
        for(String str:retSchema){
            String[] sep=str.split(":");
            if(sep.length==2) {
                ret.add(sep[0]);
            }
        }

        return ret;
    }

    public static OrcStruct newOrcStruct(OrcValue value, TypeDescription mapvalSchema) throws IOException {
        OrcStruct val = (OrcStruct) value.value;
        OrcStruct orcVal = (OrcStruct) OrcStruct.createValue(mapvalSchema);
        try{
            for (int i = 0; i < val.getNumFields(); i++) {
                TypeDescription fieldSchema = orcVal.getSchema().getChildren().get(i);
                WritableComparable fieldObj = OrcStruct.createValue(fieldSchema);
                ByteArrayOutputStream buffer = new ByteArrayOutputStream(256);
                DataOutputStream dataOutput = new DataOutputStream(buffer);
                val.getFieldValue(i).write(dataOutput);
                DataInputStream dataInputStream = new DataInputStream(new ByteArrayInputStream(buffer.toByteArray()));
                fieldObj.readFields(dataInputStream);
                dataOutput.close();
                dataInputStream.close();
                orcVal.setFieldValue(i, fieldObj);
            }
        }catch (Throwable throwable){
            throw throwable;
        }
        return orcVal;
    }
    public static String acquireSchema(String shema) throws IOException {


        List<String> fieldList = initalFields(shema);
        StringBuilder sb = new StringBuilder("struct<");
        for (String col : fieldList) {
            sb.append(col + ":string,");
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append('>');
        return sb.toString();
    }

    public static String acquireRawSchema(String shema) throws IOException {


        InputStream inputStream = getInputStreamByFileName(shema);
        List<String> retSchema = new ArrayList<String>();
        try {
            retSchema = IOUtils.readLines(inputStream, "utf8");
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
        StringBuilder sb = new StringBuilder("");
        for (String col : retSchema) {
            sb.append(col);
        }
        return sb.toString();
    }

    /**
     * assign map orc field
     * @param tagsMap
     * @param orcMap
     */
    public static  void   orcMapCopy( Map<String, String> tagsMap , OrcMap orcMap ){
        if(orcMap!=null&&tagsMap!=null) {

            if (tagsMap.size() > 0) {
                for (String elem : tagsMap.keySet()) {
                            String val=tagsMap.get(elem);
                            if(StringUtils.isEmpty(val)) {
                                val = "";
                            }
                            orcMap.put(new Text(elem), new Text(val));
                }
            }
        }
    }

    public static TypeDescription acquireSchemaType(String shema) throws IOException {


        return TypeDescription.fromString(acquireSchema(shema));
    }


    public static void main(String[] args) throws IOException {
        String p="/user/biuser/warehouse/etl/Tmp_PayDB.db/jxluo_mr_realname_result/idv_999.orc";
        if(p.matches("(idv_)+")){
            System.out.println("ok!");
        }

        // System.out.println(acquireSchema("servicecode.properties"));
//      System.out.println(acquireRawSchema("ORCDescription\\idencrypt.properties"));

//        List<String> ret = getBlacklist("ORCDescription\\idencrypt.properties");
//        System.out.println(ret.size());
//        for(String str:ret){
//            System.out.println(str);
//        }

    }

}
