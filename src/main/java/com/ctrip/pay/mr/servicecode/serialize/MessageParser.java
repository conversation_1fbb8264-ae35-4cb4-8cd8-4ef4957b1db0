package com.ctrip.pay.mr.servicecode.serialize;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.pay.mr.utils.PathUtil;
import com.google.common.base.Strings;
import org.apache.commons.lang.StringEscapeUtils;

import java.io.UnsupportedEncodingException;
import java.util.regex.Pattern;

/**
 * Created by ymfang on 2019/7/19
 */
public class MessageParser {

    static Pattern p = Pattern.compile("\\s*\\t|\\r|\\n");
    static String STRIP = "\\\\u";

    //{"value":"v=822.000_os=Android_osv=10_m=MI\\u209\\u20Transparent\\u20Edition_brand=Xiaomi_vg=20",
    public static String unicodeHandle(String message) {
        if (Strings.isNullOrEmpty(message)) {
            return "";
        }
        return message.replaceAll(STRIP, "%u"); //无法处理双斜杆的 问题
    }

    public static String replaceBlank(String str) {
        String dest = "";
        if (str != null) {

            dest = str.replaceAll("\\r\\n|\\r|\\n", " ");
        }
        return dest;
    }


    public static String parseMsg(String message) {
        if (Strings.isNullOrEmpty(message)) {
            return "";
        }
        String ret = message;
        int beg = message.indexOf("[[");
        if (beg >= 0) {
            ret = message.substring(beg).trim(); //移除字符串末尾换行
        }
        return replaceBlank(ret.trim());
    }

    /**
     * 保留的是完整的message信息，即使不是json
     *
     * @param message
     * @return
     */
    public static String parseBodyGeneral(String message) {

        if (Strings.isNullOrEmpty(message)) {
            return "";
        }

        // 处理各种斜杠问题
        try {
            message = StringEscapeUtils.unescapeJava(message);
        } catch (Exception e) {
            message = unicodeHandle(message);
            message = StringEscapeUtils.unescapeJava(message);

        }

        int bodyBeginIndex = 0;
        int tagsEndIndex = message.indexOf("]]");
        if (tagsEndIndex > 0) {
            bodyBeginIndex = tagsEndIndex + 2;
        }
        String mes = message.substring(bodyBeginIndex);
        return PathUtil.preHandleObject(mes);
    }


    /**
     * body都以json形式存在，主要用于计算支付提交率
     *
     * @param message
     * @return
     */
    public static String parseBody(String message) {
        if (Strings.isNullOrEmpty(message)) {
            return "";
        }
        // message = unicodeHandle(message);
        //处理各种斜杠问题，去除转义；
        message = StringEscapeUtils.unescapeJava(message);

        int bodyBeginIndex = 0;
        int tagsEndIndex = message.indexOf("]]");
        if (tagsEndIndex > 0) {
            bodyBeginIndex = tagsEndIndex + 2;
        }
        //移除最后一个"
        int lenth = message.lastIndexOf("}");
        String mes = bodyBeginIndex <= (lenth + 1) ? message.substring(bodyBeginIndex, lenth + 1).trim() : "";  //解决数组越界问题

//    TraceModel model = null;
//    try {
//      model = JSON.parseObject(mes, TraceModel.class);
//    } catch (Exception e) {
//
//    }
//
//      if (mes.startsWith("卡号段限制") || model == null){
//          return null;
//      }
//
//      try{
//          JSONObject request = JSON.parseObject(model.getRequest().toString());
//          model.setRequest(request);
//
//          JSONObject respnse = JSON.parseObject(model.getResponse().toString());
//          model.setResponse(respnse);
//
//          JSONObject requestHead = JSON.parseObject(model.getRequestHead().toString());
//          model.setRequestHead(requestHead);
//
//          System.out.println(model.getPlat());
//          System.out.println(model.getResultcode());
//      }catch (Exception ex){
//
//      }

        try {
            JSONObject m = JSON.parseObject(mes);
            try {//sig中的json对象被双引号包括，将其转换成json并写回
                String sig = m.getJSONObject("response").getJSONObject("thirdpartyinfo").getString("sig");
                m.getJSONObject("response").getJSONObject("thirdpartyinfo").put("sig", JSONObject.parseObject(StringEscapeUtils.unescapeJava(sig)));
            } catch (Exception ex) {

            }
            try {//DebugMessage中的json对象被双引号包括，将其转换成json并写回
                String debugMessage = m.getJSONObject("response").getString("DebugMessage");
                m.getJSONObject("response").put("DebugMessage", JSONObject.parseObject(StringEscapeUtils.unescapeJava(debugMessage)));
            } catch (Exception ex) {

            }
            try {//servicecode=31100102,response.ResponseInfo101.PayOrderInfo中的json对象被双引号包括，将其转换成json并写回
                if (message.indexOf("ResponseInfo101") > 0 && message.indexOf("PayOrderInfo") > 0) {
                    String PayOrderInfo = m.getJSONObject("response").getJSONObject("ResponseInfo101").getString("PayOrderInfo");
                    m.getJSONObject("response").getJSONObject("ResponseInfo101").put("PayOrderInfo", JSONObject.parseObject(PayOrderInfo));
                }
            } catch (Exception ex) {

            }
            mes = m.toJSONString();
        } catch (Exception ex) {

        }
        return PathUtil.preHandleObject(mes);
        // return mes;
    }

    /**
     * servicecode=31012002,目前对应appid=100013773
     *
     * @param message
     * @return
     */
    public static String parseBodyFor31012002(String message) {
        if (Strings.isNullOrEmpty(message)) {
            return "";
        }

        int bodyBeginIndex = 0;
        int tagsEndIndex = message.indexOf("]]");
        if (tagsEndIndex > 0) {
            bodyBeginIndex = tagsEndIndex + 2;
        }

        //移除最后一个"
        int lenth = message.lastIndexOf("\"");
        String mes = message.substring(bodyBeginIndex, lenth).trim();

        //处理各种斜杠问题
        int start = mes.indexOf("debugMessage");
        int end = mes.indexOf("resultCode");

        String newmes = mes.replace(mes.substring(start, end + "resultCode".length()), "resultCode"); //去掉debugMessage信息，因为里面有特殊字符影响解析

        newmes = newmes.replace("\\\"", "\""); // /"替换成" 即可

        return PathUtil.preHandleObject(newmes);
        // return mes;
    }

    /**
     * servicecode=31100102,目前对应appid=100006883
     *
     * @param message
     * @return
     */
    public static String parseBodyFor31100102(String message) {
        if (Strings.isNullOrEmpty(message)) {
            return "";
        }
        //处理各种斜杠问题
        message = message.replace("\\\\\\\\\\\\\\", "\\");
        message = message.replace("\\\\\\", "\\");
        message = message.replace("\\\\", "\\");
        message = message.replace("\\", "");

        message = message.replace("\"{", "{");
        message = message.replace("}\",", "},");

        message = message.replace("\"[", "[");
        message = message.replace("]\",", "],");

        int bodyBeginIndex = 0;
        int tagsEndIndex = message.indexOf("]]");
        if (tagsEndIndex > 0) {
            bodyBeginIndex = tagsEndIndex + 2;
        }

        //移除最后一个"
        int lenth = message.lastIndexOf("\"");
        String mes = message.substring(bodyBeginIndex, lenth).trim();

        //处理各种斜杠问题
        int start = mes.indexOf("extentionList");
        int end = mes.indexOf("language");
        String newmes = mes.replace(mes.substring(start, end + "language".length()), "language"); //去掉extentionList信息，因为里面有特殊字符影响解析

        start = newmes.indexOf("PayDisplaySetting");
        end = newmes.indexOf("OrderValidity");
        newmes = newmes.replace(newmes.substring(start, end + "OrderValidity".length()), "OrderValidity");

        return PathUtil.preHandleObject(newmes);
    }

    /**
     * servicecode=31000102,目前对应appid=100006883\100006882\411003
     *
     * @param message
     * @return
     */
    public static String parseBodyFor31000102(String message) {
        if (Strings.isNullOrEmpty(message)) {
            return "";
        }

        int bodyBeginIndex = 0;
        int tagsEndIndex = message.indexOf("]]");
        if (tagsEndIndex > 0) {
            bodyBeginIndex = tagsEndIndex + 2;
        }

        //移除最后一个"
        int lenth = message.lastIndexOf("\"");
        String mes = message.substring(bodyBeginIndex, lenth).trim();
        mes = StringEscapeUtils.unescapeJava(mes);
        return PathUtil.preHandleObject(mes);
    }

    //解析路由前置日志
    public static String parseBodyForPayDispath(String message) {
        if (Strings.isNullOrEmpty(message)) {
            return "";
        }

        int tagsEndIndex = message.indexOf("]]");
        int bodyBeginIndex = 0;
        if (tagsEndIndex > 0) {
            bodyBeginIndex = tagsEndIndex + 2;
        }
        int length = message.lastIndexOf("}");
        return bodyBeginIndex <= (length + 1) ? message.substring(bodyBeginIndex, length + 1).trim() : "";

    }

    public static String parseBodyGeneralNotJson(String message) {

        if (Strings.isNullOrEmpty(message)) {
            return "";
        }

        int bodyBeginIndex = 0;
        int tagsEndIndex = message.indexOf("]]");
        if (tagsEndIndex > 0) {
            bodyBeginIndex = tagsEndIndex + 2;
        }
        String mes = message.substring(bodyBeginIndex);
        return PathUtil.preHandleObject(mes);
    }


    public static void main(String[] args) throws UnsupportedEncodingException {

//        String message;
//        String p="serviceName:32000001\n" +
//                "request:{\"head\":{\"auth\":\"D2AF14E67937016AB4D0BD942BC2BC8CD512DCB06BCCA0F2B06FC602EBB770E5\",\"cid\":\"32001072410109564741\",\"ctok\":\"\",\"cver\":\"825.002\",\"extension\":[{\"name\":\"**l\",\"value\":\"https\"},{\"name\":\"**h\",\"value\":\"D2AF14E67937016AB4D0BD942BC2BC8CD512DCB06BCCA0F2B06FC602EBB770E5\"},{\"name\":\"**e\",\"value\":\"MemberLogin\"},{\"name\":\"**d\",\"value\":\"M2842101017\"},{\"name\":\"**h\",\"value\":\"true\"},{\"name\":\"**w\",\"value\":\"true\"}],\"lang\":\"01\",\"sid\":\"4203\",\"syscode\":\"32\"},\"plat\":11,\"reqbody\":\"{\\\"requestid\\\":\\\"ee1ce084-3539-863a-513b-159316a19565\\\",\\\"keytoken\\\":-1,\\\"keyguid\\\":-1,\\\"ver\\\":\\\"8.23\\\",\\\"cver\\\":\\\"8.25.2\\\",\\\"plat\\\":11,\\\"mchid\\\":\\\"CTRP\\\",\\\"lgtd\\\":\\\"126.71935893886338\\\",\\\"lttd\\\":\\\"45.75970239105444\\\",\\\"useragent\\\":\\\"Mozilla/5.0 (Linux; Android 9; MI 6X Build/PKQ1.180904.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.136 Mobile Safari/537.36_CtripAPP_Android_8.25.2_eb64_Ctrip_CtripWireless_8.25.2_cDevice=MI 6X_cSize=w1080*h2030__v=825.002_os=Android_osv=9_m=MI 6X_brand=xiaomi_vg=0\\\"}\",\"serviceCode\":\"32000803\",\"ver\":\"1.0\"}\n" +
//                "response:{\"rc\":-1,\"resbody\":\"{\\\"ResponseStatus\\\":{\\\"Timestamp\\\":\\\"/Date(1593844911082+0800)/\\\",\\\"Ack\\\":\\\"Failure\\\",\\\"Errors\\\":[{\\\"Message\\\":\\\"Unable to deserialize type class com.ctrip.payment.wallet.soa.userinfo.model.TouchPayVerifyRequestType\\\",\\\"ErrorCode\\\":\\\"FXD302000\\\",\\\"SeverityCode\\\":\\\"Error\\\",\\\"ErrorClassification\\\":\\\"ValidationError\\\"}]}}\",\"rmsg\":\"网络系统异常，请稍后再试\"}\n" +
//                "requesttime:2020-07-04 14:41:51\n" +
//                "responsetime:2020-07-04 14:41:51";
//        System.out.println(replaceBlank(p));

        String str = "[{\"merchantID\":200258,\"transactionID\":\"202010291050200794068\",\"returnUrl\":\"https://inn.ctrip.com/onlineinn/neworderdetail?oid=************&channelid=220\",\"language\":\"ZH\",\"description\":\"[[花筑·陶也民宿]近陶溪川三宝、步行1分钟到陶艺街、含双早、免费停车豪华家庭房\",\"orderID\":\"***********\",\"paymentDescription\":\"\",\"isGuaranteePay\":false,\"guaranteeAmount\":0,\"amount\":1064.0,\"currency\":\"CNY\",\"enableInvoice\":true,\"customerID\":\"*********\",\"customerName\":\"*********\",\"enableTicketPay\":true,\"ticketType\":\"2,3\",\"enabledPayCatalog\":\"CreditCard,DepositCard,EBank\",\"enabledPayWay\":\"{\\\"EBank\\\":\\\"WechatScanCode,Alipay\\\"}\",\"disabledPayWay\":\"\",\"cardHolder\":\"\",\"creditCardNoRange\":\"\",\"buyerIP\":\"*************\",\"relatedTransactionID\":\"\",\"previousStepUrl\":\"https://inn.ctrip.com/onlineinn/neworderdetail?oid=************&channelid=220\",\"guaranteeCurrency\":\"\",\"enabledCardExpirationDate\":\"\",\"interfaceName\":\"Create_PaymentBill\",\"invoiceSummary\":\"\",\"payDiscountRange\":\"\",\"payRequestExtend\":\"EnabledAccountPwd=&ManagedVersion=&FullVounchPayShowText=&VoucherMixedDisabledPayWay=&MayReceiveBranch=&MayReceiveSite=&PayDeadLine=&RecallType=SOA20:http://webapi.soa.ctripcorp.com/api/17159/paymentResultNotify&BatchDocumentNo=&BatchDocumentType=&TMLimitAmount=&DepositAmount=&CreditNeed=&CheckPayableTo=&CollectRecall=&OldCardInfoID=&BusinessExchangeRate=1&NeedCallRiskControl=T&BillDesc=&Credit=0&IsRealTimePay=1&IsAutoApplyBill=1&PaymentNotify=http://webapi.soa.ctripcorp.com/api/17159/paymentBeforeNotifyForPC&ReturnMethod=POST&ContactsInfoUrl=&PaymentAddressUrl=&Payee=1&UpdateBillMode=1&PrevTransactionID=&PrevBillNo=&IsReUseAmount=&SupportChangePayway=&UsePrevPaywayMode=1&HideReceiveSite=0&PaywayOptionMode=1&PayTimeLimit=&BillingAddrDetail=&DirectPay=&InsuranceInfos=&IsAppPaymentNotify=\",\"enableCashAccountPay\":true,\"externalNo\":\"\",\"requestSource\":\"20\",\"orderTitleInfo\":\"***\",\"orderPromptInfo\":\"\",\"paymentVersion\":\"2.01\",\"isNeedPreAuth\":false,\"marketingPlan\":\"\"},{\"customerId\":\"*********\",\"merchantId\":\"200258\",\"orderId\":\"***********\",\"requestId\":\"0\",\"language\":\"ZH\",\"platform\":\"20\",\"clientIp\":\"*************\",\"serviceCode\":\"********\",\"guid\":\"83b34ab9-0645-4653-9f81-03f924bee58e\",\"cbuToken\":\"\"}]";
        try {
            System.out.println(parseMsg(str));
        } catch (Exception e) {
            e.printStackTrace();

        }


    }
}
