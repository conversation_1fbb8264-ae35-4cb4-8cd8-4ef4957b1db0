package com.ctrip.pay.mr.map;

import com.alibaba.fastjson.JSON;
import com.ctrip.pay.mr.Entity.BatchEncryptDto;
import com.ctrip.pay.mr.Entity.BatchEncryptRequestType;
import com.ctrip.pay.mr.Entity.BatchEncryptResponseType;
import com.ctrip.pay.mr.Entity.EncryptResultDto;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

public class CtripEncrypt {

    private static String urlBatchStr = "http://coreinfo.offline.arc.ctripcorp.com/encryptservice/api/encode";
    //测试环境
//    private static String urlBatchStr = "http://coreinfo.arc.fws.qa.nt.ctripcorp.com/encryptservice/api/encode";

    public static List<EncryptResultDto> encode(BatchEncryptRequestType request){
        if(request==null || request.getInfos()==null){
            return null;
        }
        BatchEncryptResponseType response;
        List<EncryptResultDto> results = new ArrayList<>();

        CloseableHttpClient client = HttpClientBuilder.create().build();
        HttpPost post = new HttpPost(urlBatchStr);

        try {
            post.addHeader("X-Client-AppId", "100017408");
            post.addHeader("Content-Type", "application/json");
            post.setEntity(new StringEntity(JSON.toJSONString(request), Charset.forName("UTF-8")));
            HttpResponse resp=client.execute(post);

            if(resp != null) {
                response = JSON.parseObject(EntityUtils.toString(resp.getEntity()),BatchEncryptResponseType.class);
                if (response!=null && response.getResponseStatus().getAck().equals("Success")) {
                    results=response.getResults();
                }else {
//                    System.out.println(response.getRetMsg());
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }

        return results;
    }

    public static void  main(String[] args) {
        List<BatchEncryptDto> list = new ArrayList<>();
        BatchEncryptRequestType requestType = new BatchEncryptRequestType();

        BatchEncryptDto para= new BatchEncryptDto();
        para.setType("1");
        para.setKey("18621766865");

        list.add(para);

        para= new BatchEncryptDto();
        para.setType("1");
        para.setKey("17717373865");

        list.add(para);

        requestType.setInfos(list);

        List<EncryptResultDto> results = encode(requestType);

        for(EncryptResultDto dto : results){
            System.out.println(dto.getRawKey().getKey()+"   "+dto.getResult());
        }
    }
}
