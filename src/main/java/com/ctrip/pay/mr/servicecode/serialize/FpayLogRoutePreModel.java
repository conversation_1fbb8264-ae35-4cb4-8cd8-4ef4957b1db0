package com.ctrip.pay.mr.servicecode.serialize;

import java.util.Date;

public class FpayLogRoutePreModel {


    private String command;
    private Object request;
    private Object response;
    private Date start;
    private Date end;
    private String span;
    private String appId;

    public FpayLogRoutePreModel() {
    }

    public FpayLogRoutePreModel(String command, Object request, Object response, Date start, Date end, String span, String appId) {
        this.command = command;
        this.request = request;
        this.response = response;
        this.start = start;
        this.end = end;
        this.span = span;
        this.appId = appId;
    }

    public String getCommand() {
        return command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public Object getRequest() {
        return request;
    }

    public void setRequest(Object request) {
        this.request = request;
    }

    public Object getResponse() {
        return response;
    }

    public void setResponse(Object response) {
        this.response = response;
    }

    public Date getStart() {
        return start;
    }

    public void setStart(Date start) {
        this.start = start;
    }

    public Date getEnd() {
        return end;
    }

    public void setEnd(Date end) {
        this.end = end;
    }

    public String getSpan() {
        return span;
    }

    public void setSpan(String span) {
        this.span = span;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    @Override
    public String toString() {
        return "FpayLogRoutePreModel{" +
                "command='" + command + '\'' +
                ", request=" + request +
                ", response=" + response +
                ", start=" + start +
                ", end=" + end +
                ", span='" + span + '\'' +
                ", appId='" + appId + '\'' +
                '}';
    }
}
