package com.ctrip.pay.mr.servicecode;

import com.ctrip.pay.mr.inputformat.CombinedOrcInputFormat;
import com.ctrip.pay.mr.reducer.OrcWriteMultioutputReducer;
import com.ctrip.pay.mr.servicecode.serialize.MessageParser;
import com.ctrip.pay.mr.servicecode.serialize.TagParser;
import com.ctrip.pay.mr.utils.DateUtil;
import com.ctrip.pay.mr.utils.FileUtils;
import com.ctrip.pay.mr.utils.JsonUtils;
import com.ctrip.pay.mr.utils.MRUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.io.compress.CompressionCodec;
import org.apache.hadoop.io.compress.SnappyCodec;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.LazyOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.hadoop.util.GenericOptionsParser;
import org.apache.hadoop.util.Tool;
import org.apache.hadoop.util.ToolRunner;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;
import org.apache.orc.mapreduce.OrcOutputFormat;
import org.codehaus.jackson.map.ObjectMapper;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;


public class fnc_log_client_route_analysis extends Configured implements Tool {
    public static String schemaPath = "ORCDescription/fnc_log_client_route_analysis.properties";

    public static class UnionLogMapper
            extends Mapper<NullWritable, OrcStruct, Text, OrcValue> {
        int count = 0;
        private Text outKey = new Text();
        private OrcValue outValue = new OrcValue();
        //对应hive表字段
        private List<String> fieldList = new ArrayList<>();
        //对应orc表数据结构
        private OrcStruct pair = null;
        private String schema = "";


        @Override
        protected void setup(Context context) throws IOException, InterruptedException {
            schema = context.getConfiguration().get("orc.mapred.output.schema");
            System.out.println("start to step up schema:" + schema);
            fieldList = MRUtils.initalFields(schemaPath);
            TypeDescription typeDescription = TypeDescription.fromString(schema);
            typeDescription.createRowBatch(100);
            pair = (OrcStruct) OrcStruct.createValue(typeDescription);
        }

        @Override
        protected void map(NullWritable key, OrcStruct value, Context context) throws IOException, InterruptedException {
            String msg = MessageParser.parseMsg(value.getFieldValue(0).toString().trim()); //移除特殊字符
            //解析tag
            Map<String, String> tagsMap = TagParser.parseTags(msg);
            if (tagsMap.keySet().size() == 0) {
                return;
            }
            //解析msg
            String bodyStr = MessageParser.parseBodyGeneral(msg);
            String uid = tagsMap.get("uid") == null ? "" : tagsMap.get("uid");
            String appid = tagsMap.get("paymentappid") == null ? "" : tagsMap.get("paymentappid");
            if (!appid.matches("(.*)[0-9]+(.*)")) {  //非appid删除
                System.out.println("abandom:" + uid + "_" + appid);
                return;
            }

            Date d = DateUtil.getDateLong(tagsMap.get("logtime"));

            //不能用_作为分隔符，因为部分uid就是以_开头的
            outKey.set(uid + MRUtils.col_seperator + appid + MRUtils.col_seperator + DateUtil.getDateLong(d));

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < fieldList.size(); i++) {
                String str = fieldList.get(i);
                if (str.equals("tag")) {
                    String jsonStr = new ObjectMapper().writeValueAsString(tagsMap);
                    pair.setFieldValue(i, new Text(jsonStr));
                    sb.append(str + " = " + jsonStr);
                    continue;
                }

                String strVal = tagsMap.get(str);
                if (str.equals("logtime")) {
                    Date param = null;
                    if (tagsMap.get("logtime") != null) {
                        param = new Date(Timestamp.valueOf(tagsMap.get("logtime")).getTime());
                    }
                    strVal = DateUtil.format(param);
                }
                sb.append(str + " = " + strVal);
                if (strVal == null) {
                    strVal = "";
                }
                pair.setFieldValue(i, new Text(strVal));

            }

            if (count < 3) {
                if (sb.length() < 2000) {
                    System.out.println("key" + outKey.toString() + "mapvalue:" + sb.toString());
                } else {
                    System.out.println("key" + outKey.toString() + "mapvalue:" + sb.toString().substring(1, 2000));
                }
            }
            count++;
            //测试方便只用
            outValue.value = pair;
            context.write(outKey, outValue);
        }
    }

    @Override
    public int run(String[] otherArgs) throws Exception {
        Configuration conf = getConf();
        if (otherArgs.length != 1) {
            System.out.println("argument is wrong,single argument is required");
            return 0;
        }
        String schema = MRUtils.acquireRawSchema(schemaPath);

        conf.set("mapreduce.input.fileinputformat.split.maxsize", "134217728");
        conf.set("mapreduce.task.io.sort.mb", "200");
        conf.setBoolean("mapreduce.map.output.compress", true);
        conf.setClass("mapreduce.map.output.compress.code", SnappyCodec.class, CompressionCodec.class);
        //map内存调大
        conf.set("mapreduce.map.memory.mb", "8096");
        conf.set("mapreduce.map.java.opts", "-Xmx6000m");
        //reduce内存调大
        conf.set("mapreduce.reduce.memory.mb", "8096");
        conf.set("mapreduce.reduce.java.opts", "-Xmx6000m");
        conf.set("mapreduce.reduce.shuffle.memory.limit.percent", "0.15");
//        conf.set("mapreduce.reduce.shuffle.merge.percent", "0.2");
        conf.set("mapreduce.reduce.speculative", "false");

        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(conf, schema);
        OrcConf.MAPRED_SHUFFLE_VALUE_SCHEMA.setString(conf, schema);
        conf.setBoolean(OrcOutputFormat.SKIP_TEMP_DIRECTORY, true);
        conf.set("mapreduce.map.output.value.class", "org.apache.orc.mapred.OrcValue");

        System.out.println("param1:" + otherArgs[0]);
        FileSystem fs = FileSystem.get(conf);
        Job job = Job.getInstance(conf, "pay log etl");
        job.setReduceSpeculativeExecution(false);
        job.setJarByClass(fnc_log_client_route_analysis.class);
        job.setMapperClass(UnionLogMapper.class);
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(OrcValue.class);
        job.setInputFormatClass(CombinedOrcInputFormat.class);
        job.setReducerClass(OrcWriteMultioutputReducer.class);
        job.setOutputKeyClass(NullWritable.class);
        job.setOutputValueClass(OrcStruct.class);

        job.setNumReduceTasks(500);
        String root = "/user/bifin/pay/topic/data/fnc_log_client_route";

        List<String> partitionLists = FileUtils.generateRdInputPrefix2(root, otherArgs[0], otherArgs[0]);
        Collections.shuffle(partitionLists);
        MRUtils.addInputDirs(partitionLists, fs, job);

        System.out.println("输入文件个数" + partitionLists.size());
        if (partitionLists.size() > 0) {
            System.out.println("输入文件格式" + partitionLists.get(0));
        } else {
            System.out.println("输入文件数为0");
            return -1;

        }

        String outputPath = String.format("/user/biuser/warehouse/etl/Ods_PayDB.db/fnc_log_client_route_analysis_2" + "/d=%s/", otherArgs[0]);
        job.getConfiguration().set("fpaylog.path", outputPath);
        FileOutputFormat.setOutputPath(job, new Path(outputPath));
        MultipleOutputs.addNamedOutput(job, "paylog", OrcOutputFormat.class, NullWritable.class, OrcStruct.class);
        LazyOutputFormat.setOutputFormatClass(job, OrcOutputFormat.class);
        System.out.println("start to delete output:" + outputPath);

        boolean fileDelete = fs.delete(new Path(outputPath), true);
        if (fileDelete) {
            System.out.println("delete output:" + outputPath + " successful");
        } else {
            System.out.println("delete output:" + outputPath + " failed");
        }
        if (otherArgs[otherArgs.length - 1].equals("@")) {
            job.submit();
            return 0;
        } else {
            return job.waitForCompletion(true) ? 0 : 1;
        }
    }

    public static void main(String[] args) throws Exception {
        Configuration conf = new Configuration();
        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
        System.exit(ToolRunner.run(conf, new fnc_log_client_route_analysis(), otherArgs));
    }
}
