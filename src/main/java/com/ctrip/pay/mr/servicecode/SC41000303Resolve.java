package com.ctrip.pay.mr.servicecode;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.pay.mr.servicecode.serialize.TraceModel;
import com.ctrip.pay.mr.utils.DateUtil;
import com.ctrip.pay.mr.utils.FileUtils;
import com.ctrip.pay.mr.utils.MRUtils;
import com.google.common.collect.Maps;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.util.GenericOptionsParser;
import org.apache.hadoop.util.Tool;
import org.apache.hadoop.util.ToolRunner;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcMap;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;
import org.apache.orc.mapreduce.OrcOutputFormat;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SC41000303Resolve extends Configured implements Tool {
    public static String schemaPath = "ORCDescription/servicecode.properties";
    public static class SC41000303Mapper
            extends Mapper<LongWritable, Text, Text, OrcValue> {
        int count = 0;
        private Text outKey = new Text();
        private OrcValue outValue = new OrcValue();
        //对应hive表字段
        private List<String> fieldList = new ArrayList<>();
        private Map<String, String> fieldValue = Maps.newHashMap();
        //对应orc表数据结构
        private OrcStruct pair = null;
        private String schema = "";


        @Override
        protected void setup(Context context) throws IOException, InterruptedException {

            fieldList = MRUtils.initalFields(schemaPath);
            schema = MRUtils.acquireRawSchema(schemaPath);
            pair = (OrcStruct) OrcStruct.createValue(TypeDescription.fromString(schema));
        }

        @Override
        protected void map(LongWritable key, Text value, Context context) throws IOException, InterruptedException {
            String msg = value.toString().trim();
            if (msg.length() < 20) { //异常行
                return;
            }
            if( msg.indexOf("41000303")<0 || msg.indexOf("cardPrepayment-Request")>0 || msg.indexOf("cardPrepayment-Response")>0 ){
                return;
            }
            if( msg.indexOf("paymentSubmit-Request")<0 && msg.indexOf("paymentSubmit-Response")<0
                && msg.indexOf("payment-Request")<0 && msg.indexOf("payment-Response")<0){  //新增落地日志
                return;
            }

            //解析tag
            Map<String, String> tagsMap = new HashMap<String, String>();

            //解析msg
            String bodyStr = msg;
            //System.out.println("my msg is : "+bodyStr);

            //解析报文体部分
            JSONObject m = JSON.parseObject(msg);
            String payload="";
            try {
                payload = m.getJSONObject("stored").getString("payload");
            }catch(Exception ex){

            }
            JSONObject mm = JSON.parseObject(msg);
            tagsMap.put("servicecode", mm.getString("functionid"));
            tagsMap.put("functionname", mm.getString("functionname"));
            if(mm.getString("paymentAppid")!=null)
                tagsMap.put("paymentAppid", mm.getString("paymentAppid"));
            else if(mm.getString("ModuleID")!=null)
                tagsMap.put("paymentAppid", mm.getString("ModuleID"));

            tagsMap.put("merchantid", mm.getString("merchantid"));
            if(mm.getString("uid")!=null)
                tagsMap.put("uid", mm.getString("uid"));
            else if(mm.getString("CustomerID")!=null)
                tagsMap.put("uid", mm.getString("CustomerID"));
            tagsMap.put("orderid", mm.getString("orderid"));
            tagsMap.put("logtype", mm.getString("logtype"));
            tagsMap.put("requestid", mm.getString("requestid"));
            tagsMap.put("messageId", mm.getString("messageId"));

            tagsMap.put("beginTime", mm.getString("createtime"));
            tagsMap.put("endTime", mm.getString("updatetime"));
            //tagsMap.put("plat", "onlineapi");
            //tagsMap.put("syscode", "01"); //直接定义为01 online
            //outKey.set(mm.getString("uid"));
            if(mm.getString("uid")!=null)
                outKey.set(mm.getString("uid"));
            else if(mm.getString("CustomerID")!=null)
                outKey.set(mm.getString("CustomerID"));

            OrcConf.MAPRED_SHUFFLE_VALUE_SCHEMA.setString(context.getConfiguration(), schema);
            OrcConf.MAPRED_OUTPUT_SCHEMA.setString(context.getConfiguration(), schema);

            //赋值uid、orderid、bustype,syscode
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < fieldList.size(); i++) {
                String str = fieldList.get(i);
                if (str.equals("tag")) {
                    OrcMap orcMap = (OrcMap) pair.getFieldValue(i);

                    MRUtils.orcMapCopy(tagsMap,orcMap);
                    pair.setFieldValue(i, orcMap);
                    continue;
                }

                String strVal = tagsMap.get(str);
                if (str.equals("plat")) {
                    strVal = "onlineapi";
                }else if (str.equals("syscode")) {
                    strVal = "01";
                }if (str.equals("request") && (msg.indexOf("paymentSubmit-Request")>0 || msg.indexOf("payment-Request")>0)) {
                    strVal = payload;
                } else if (str.equals("response") && (msg.indexOf("paymentSubmit-Response")>0 || msg.indexOf("payment-Response")>0)) {
                    strVal = payload;
                } else if (str.equals("requesttime")) {
                    strVal = mm.getString("createtime");
                } else if (str.equals("message")) {
                    strVal = bodyStr;
                } else if (str.equals("message_raw")) {
                    strVal = msg;
                }
                sb.append(str + " = " + strVal);
                if (strVal == null) {
                    strVal = "";
                }
                pair.setFieldValue(i, new Text(strVal));

            }
            outValue.value = pair;

            if (count < 3) {
                if (sb.length() < 2000) {
                    System.out.println("mapvalue:" + sb.toString());
                } else {
                    System.out.println("mapvalue:" + sb.toString().substring(1, 2000));
                }
            }
            count++;
            //测试方便只用
            //  if (count > 1000) return;
            fieldValue.clear(); //清空
            context.write(outKey, outValue);
        }
    }


    @Override
    public int run(String[] otherArgs) throws Exception {
        Configuration conf = getConf();
        if (otherArgs.length != 2) {
            System.out.println("argument is wrong,two argument is needed");
            return 0;
        }
        String schema = MRUtils.acquireRawSchema(schemaPath);
        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(conf, schema);
        conf.set("orc.mapred.output.schema", schema);
        conf.set("orc.mapred.map.output.value.schema", schema);

        System.out.println("param1:" + otherArgs[0]);
        System.out.println("param2:" + otherArgs[1]);
        FileSystem fs = FileSystem.get(conf);
        Job job = Job.getInstance(conf, otherArgs[0] + "_" + otherArgs[1] + " jiexi");
        job.setJarByClass(SC41000303Resolve.class);
        job.setMapperClass(SC41000303Mapper.class);
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(OrcValue.class);
        job.getConfiguration().set("orc.mapred.value.type", "OrcValue");

        job.setReducerClass(OrcWriteReducer.class);
        job.setOutputFormatClass(OrcOutputFormat.class);

        job.setNumReduceTasks(300);
        List<String> partitionLists = FileUtils.generateOnlineInput(otherArgs[0], otherArgs[1],otherArgs[1]);
        System.out.println("输入文件数量:" + partitionLists.size());
        MRUtils.addInputDirs(partitionLists, fs, job);
        String outputPath = FileUtils.getOutputDir(otherArgs[0], otherArgs[1]);
        OrcOutputFormat.setOutputPath(job, new Path(outputPath));

        System.out.println("start to delete output:" + outputPath);

        boolean fileDelete = fs.delete(new Path(outputPath), true);
        if (fileDelete) {
            System.out.println("delete output:" + outputPath + " successful");
        } else {
            System.out.println("delete output:" + outputPath + " failed");
        }
        if (otherArgs[otherArgs.length - 1].equals("@")) {
            job.submit();
            return 0;
        } else {
            return job.waitForCompletion(true) ? 0 : 1;
        }
    }


    public static void main(String[] args) throws Exception {
        Configuration conf = new Configuration();
        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
        System.exit(ToolRunner.run(conf, new SC41000303Resolve(), otherArgs));
    }

}