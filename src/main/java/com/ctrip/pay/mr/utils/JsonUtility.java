package com.ctrip.pay.mr.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;

public class JsonUtility {
  private static Gson gson = new GsonBuilder().serializeNulls().create();

  public static String toJson(Object src) {
    if (src == null) {
      return "";
    }

    return gson.toJson(src);
  }

  public static <T> T fromJson(String json, Class<T> classOfT) throws JsonSyntaxException {
    return gson.fromJson(json, classOfT);
  }
}
