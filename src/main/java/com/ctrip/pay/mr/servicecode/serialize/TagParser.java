package com.ctrip.pay.mr.servicecode.serialize;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.pay.mr.utils.DateUtil;
import com.ctrip.pay.mr.utils.MRUtils;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by ymfang on 2019/07/18.
 */
public class TagParser {
    public static Map<String, String> parseTags(String tagsStr) {
        Map<String, String> tags = new ConcurrentHashMap<>();

        if (Strings.isNullOrEmpty(tagsStr)) {
            return tags;
        }

        int tagStart = tagsStr.indexOf("[[") + 2;
        int tagsEndIndex = tagsStr.indexOf("]]");
        int tagEnd = 0;
        for (; tagStart < tagsEndIndex; tagStart = tagEnd + 1) {
            tagEnd = tagsStr.indexOf(44, tagStart);  //44是逗号的ASCII码值
            if (tagEnd < 0 || tagEnd > tagsEndIndex) {
                tagEnd = tagsEndIndex;
            }

            int equalIndex = tagsStr.indexOf(61, tagStart); //61是等号的ASCII码值
            if (equalIndex > tagStart && equalIndex < tagEnd - 1) {
                String key = tagsStr.substring(tagStart, equalIndex);
                String value = tagsStr.substring(equalIndex + 1, tagEnd);

                tags.put(key.toLowerCase(), value);
            }
        }

        return tags;
    }

    /**
     * hjliu
     * 解析出报文里面的tag属性列表
     *
     * @param tagsStr
     * @return
     */
    public static Map<String, String> parseTagsMap(String tagsStr) {
        Map<String, String> tags = new ConcurrentHashMap<>();

        if (Strings.isNullOrEmpty(tagsStr)) {
            return tags;
        }

        int tagStart = tagsStr.indexOf("[[") + 2;
        int tagsEndIndex = tagsStr.indexOf("]]");

        String content = tagsStr.substring(tagStart, tagsEndIndex);
        String arr[] = content.split(",");
        for (int i = 0; i < arr.length; i++) {
            System.out.println(arr[i]);
            String map[] = arr[i].split("=");
            if (map.length > 1) {
                tags.put(map[0], map[1]);

            } else {
                tags.put(map[0], "");
            }
        }
        return tags;
    }

    public static void main(String[] args) {
        String str = "{\"cat_client_bu\":\"支付中心\",\"uid\":\"M3722612241\",\"logtype\":\"20\",\"esbreferenceid\":null,\"functionname\":\"logConfig\",\"clientip\":\"************\",\"serverip\":\"**********\",\"errorcode\":\"\",\"createtime\":\"2020-07-06 07:01:20\",\"cat_client_appid\":\"*********\",\"orderid\":\"13096213299\",\"ip\":\"**********\",\"messageId\":\"921814-0a3e0307-442775-1103\",\"currentpage\":null,\"paymentAppid\":\"*********\",\"functionid\":\"41000303\",\"@timestamp\":1593990080188,\"merchantid\":\"200003\",\"requestid\":\"5-729592794586648576\",\"loglevel\":\"1\",\"stored\":{\"payload\":\"AppID:********* /r/nSubEnv:null /r/nServiceRequestEnv:PRO /r/nESBUrl:http://online.soa.sh.ctriptravel.com/SOA.ESB/Ctrip.SOA.ESB.asmx /r/nMerchantPaymentwayListUrl:https://ws.proxy.router.payment.ctripcorp.com/payment-route-apiservice/merchant/paymentwaylist /r/nCardNoMatchingUrl:https://ws.proxy.router.payment.ctripcorp.com/payment-route-apiservice/card/ctripmatch /r/nUsedCardListUrl:https://ws.proxy.router.payment.ctripcorp.com/payment-route-apiservice/card/usedcardlist /r/nFieldMatchUrl:https://ws.proxy.router.payment.ctripcorp.com/payment-route-apiservice/card/fieldmatch /r/nPaymentRestService:https://ws.api.payment.ctripcorp.com/payment-rest-service /r/nFxConfigServiceUrl:http://ws.config.framework.ctripcorp.com/configws/ /r/nLoggingServerV2IP:collector.logging.sh.ctriptravel.com /r/nLoggingServerV2Port:63100 /r/nWalletMerchantID:CTRP /r/nRedisCacheName:FncOnlinePaymentCache /r/nTransactionPasswordUrl:https://secure.ctrip.com/webwallet/safety/index /r/nTransactionPasswordUrlEn:https://accounts.ctrip.com/global/english/MemberCenter/RebindingProfileInfo /r/nEBankPayReturnUrl:https://secure.ctrip.com/RealTimePay/EBankPayReturn /r/nEBankPayReturnUrlEn:https://global.secure.ctrip.com/PaymentGlobalizationOnline/EBankPayReturn /r/nEBankPayReturnUrlCheetah:https://secure.ctrip.com/RealTimePay/QunarReturn /r/nDisabledUsedCardMerchant:[200079] /r/nRiskverifyURL:http://ws.event.infosec.ctripcorp.com/eventws/v2/riskverify /r/n\"},\"guid\":\"c8be5c14-8f59-433e-bf39-a9085b21c6c8\",\"interval\":\"0\",\"updatetime\":\"2020-07-06 07:01:20\",\"logtitle\":\"记录配置数据\"}\n";

        JSONObject m = JSON.parseObject(str);
        System.out.println(m.get("uid"));
        Map<String, String> tagsMap = TagParser.parseTags(str);
        if (tagsMap.keySet().size() == 0) {
            return;
        }
        //解析msg
        String bodyStr = MessageParser.parseBodyGeneral(str);
        String uid = tagsMap.get("uid") == null ? "" : tagsMap.get("uid");
        String appid = tagsMap.get("paymentappid") == null ? "" : tagsMap.get("paymentappid");
        if (!appid.matches("(.*)[0-9]+(.*)")) {  //非appid删除
            System.out.println("abandom:" + uid + "_" + appid);
            return;
        }
        Date d = DateUtil.getDateLong(tagsMap.get("logtime"));
        String key = uid + MRUtils.col_seperator + appid + MRUtils.col_seperator + DateUtil.getDateLong(d);
        System.out.println(key);

        Splitter splitter = Splitter.on(MRUtils.col_seperator).trimResults();
        String[] fields = Lists.newArrayList(splitter.split(key.toString())).toArray(new String[]{});
        if (fields == null || fields.length != 3) {
            return;
        }
    }
}
