package com.ctrip.pay.mr.utils;

import com.google.common.collect.Lists;
import org.apache.orc.TypeDescription;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;

/**
 * @Author: ymfang
 * @Date: 2020/8/19 12:30
 */
public class HiveUtils {

    public static String createTableSql(String dbName,String tableName,TypeDescription newSchema) {
        StringBuilder sb = new StringBuilder("use "+dbName+";drop table if exists "+ tableName +";"+" create table " + tableName + "(");
        int index = 0;
        List<String> filedsName = newSchema.getFieldNames();
        String colName;
        for (TypeDescription typeDescription : newSchema.getChildren()) {
            colName = filedsName.get(index).replaceAll("\\.","_");
            sb.append("`"+colName + "` " + typeDescription.toString() + " COMMENT '" + colName + "'");
            if (index < filedsName.size() - 1) sb.append(",");
            else sb.append(")");
            index++;
        }
        sb.append(" COMMENT '"+tableName+"'");
        sb.append(" STORED AS ORC;");
        return sb.toString();
    }

    public static String loadTableSql(String dbName,String tableName, String dataPath) {
        StringBuilder sb = new StringBuilder("load data inpath 'hdfs://ns/"+dataPath+"' overwrite into table "+dbName+"."+tableName);
        sb.append(";");
        return sb.toString();
    }
    public static int loadDataIntoTable(String dbName,String tableName, String schema,String dataPath) {
        TypeDescription typeDescription = TypeDescription.fromString(schema);
        String createdSql = createTableSql(dbName,tableName, typeDescription);
        System.out.println("start create table: "+createdSql);
        int rst = executeSql(createdSql);
        if(rst==1)  return 1;

        String loadSql = loadTableSql(dbName, tableName,dataPath);
        System.out.println("start load table: "+loadSql);

        return executeSql(loadSql);
    }



    public static int executeSql(String sql) {
        try{
            List<String> command = Lists.newArrayListWithCapacity(3);
            command.add("hive");
            command.add("-e");
            command.add(sql);
            ProcessBuilder hiveProcessBuilder = new ProcessBuilder(command);
            Process hiveProcess = hiveProcessBuilder.start();
            int rt = hiveProcess.waitFor();
            if(rt==1)  outputErrorInfo(hiveProcess.getErrorStream());
            return rt;
        }catch (Exception exp){
            throw new RuntimeException("executeSql ERROR:"+exp.getMessage());
        }

    }

    private static void outputErrorInfo(InputStream errorStream) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(errorStream));
        String data = br.readLine();
        while(data!=null){
            data = br.readLine();
        }
    }

}
