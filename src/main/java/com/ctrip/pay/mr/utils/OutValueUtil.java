package com.ctrip.pay.mr.utils;

import com.alibaba.fastjson.JSON;
import com.ctrip.pay.mr.servicecode.serialize.TraceModel;
import org.apache.hadoop.io.Text;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcMap;
import org.apache.orc.mapred.OrcStruct;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

public class OutValueUtil {
    public static OrcStruct fillOutValue(Map<String, String> tagsMap, List<String> fieldList, TraceModel model, String bodyStr, String msg)
        throws IOException, InterruptedException{
        String schema = MRUtils.acquireRawSchema("ORCDescription/servicecode.properties");
        OrcStruct pair = (OrcStruct) OrcStruct.createValue(TypeDescription.fromString(schema));
        //赋值uid、orderid、bustype,syscode
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < fieldList.size(); i++) {
            String str = fieldList.get(i);
            if (str.equals("tag")) {
                OrcMap orcMap = (OrcMap) pair.getFieldValue(i);
                if (tagsMap.size() > 0) {
                    for (String elem : tagsMap.keySet()) {
                        orcMap.put(new Text(elem), new Text(tagsMap.get(elem)));
                    }
                }else{
                    orcMap.put(new Text(""), new Text(""));
                }
                pair.setFieldValue(i, orcMap);
                continue;
            }

            String strVal = tagsMap.get(str);
            if (str.equals("request")) {
                strVal = JSON.toJSONString(model.getRequest());
            } else if (str.equals("response")) {
                strVal = JSON.toJSONString(model.getResponse());
            } else if (str.equals("requesttime") && model.getBeginTime() != null) {
                strVal = DateUtil.dateFormat.format(model.getBeginTime());
            } else if (str.equals("message")) {
                strVal = bodyStr;
            } else if (str.equals("message_raw")) {
                strVal = msg;
            }
            sb.append(str + " = " + strVal);
            if (strVal == null) {
                strVal = "";
            }
            pair.setFieldValue(i, new Text(strVal));
        }

        return pair;
    }
}