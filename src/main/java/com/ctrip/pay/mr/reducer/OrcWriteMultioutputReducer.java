package com.ctrip.pay.mr.reducer;

import com.ctrip.pay.mr.utils.FileUtils;
import com.ctrip.pay.mr.utils.MRUtils;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import org.apache.hadoop.mapreduce.TaskID;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;

import java.io.IOException;

/**
 * @Author: ymfang
 * @Date: 2019/7/18 14:52
 */
public class OrcWriteMultioutputReducer
        extends Reducer<Text, OrcValue, NullWritable, OrcStruct> {
    private int count = 0;
    private String schema = "";
    private int bucketNum = 20;
    private MultipleOutputs<NullWritable, OrcStruct> multipleOutputs;
    private FileSystem fs;
    private static String PREFIX = "/user/biuser/warehouse/etl/Ods_PayDB.db/fpaylog_raw/";

    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        schema = MRUtils.acquireSchema("ORCDescription/unionlog.properties");
        multipleOutputs = new MultipleOutputs(context);
        TypeDescription typeDescription = TypeDescription.fromString(schema);
        typeDescription.createRowBatch(100);
    }

    @Override
    public void cleanup(Context context) throws IOException, InterruptedException {
        multipleOutputs.close();

    }

    @Override
    protected void reduce(Text key, Iterable<OrcValue> values, Context context) throws IOException, InterruptedException {

        Splitter splitter = Splitter.on(MRUtils.col_seperator).trimResults();
        String[] fields = Lists.newArrayList(splitter.split(key.toString())).toArray(new String[]{});
        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(context.getConfiguration(), schema);
        int index = 0;
        for (OrcValue itm : values) {
            if (fields.length == 3) {
                if ((count++) < 3) {
                    String path = context.getConfiguration().get("fpaylog.path") + "app_bucket=" + index + "/";
                    TaskID taskId = context.getTaskAttemptID().getTaskID();
                    String fileName = "-r-00" + FileUtils.getMaskFileName(taskId.getId()) + ".orc";

                    String filePath = path + fileName;
                    System.out.println("InnerReducer:" + count + ": uid=" + key + "  value:" + itm.toString() + " appid:" + fields[1]
                            + "filePath:" + filePath);
                }
                try {
                    index = Integer.parseInt(fields[1]) % bucketNum;
                    multipleOutputs.write("paylog", NullWritable.get(), (OrcStruct) itm.value, "app_bucket=" + index + "/");
                } catch (Exception e) {
                    String path = context.getConfiguration().get("fpaylog.path") + "app_bucket=" + index + "/";
                    TaskID taskId = context.getTaskAttemptID().getTaskID();
                    String fileName = "-r-00" + FileUtils.getMaskFileName(taskId.getId()) + ".orc";

                    String filePath = path + fileName;
                    Configuration conf = context.getConfiguration();
                    fs = FileSystem.get(conf);
                    boolean fileDelete = fs.delete(new Path(filePath), true);
                    if (fileDelete) {
                        System.out.println("delete output:" + filePath + " successful");
                    } else {
                        System.out.println("delete output:" + filePath + " failed");
                    }

                }


            }
        }


    }
}