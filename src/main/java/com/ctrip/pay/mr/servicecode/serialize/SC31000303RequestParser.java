package com.ctrip.pay.mr.servicecode.serialize;

import com.alibaba.fastjson.JSON;

public class SC31000303RequestParser {
    private String orderid;
    private String bustype;
    private String plat;
    private String paytype;
    private String requestid;
    private String usetype;
    private String subusetype;
    private String subpay;
    private thirdpartyinfo thirdpartyinfo;
    private bankcardinfo bankcardinfo;
    private cardinfo cardinfo;

    public String getOrderid() {
        return orderid;
    }

    public void setOrderid(String orderid) {
        this.orderid = orderid;
    }

    public String getBustype() {
        return bustype;
    }

    public void setBustype(String bustype) {
        this.bustype = bustype;
    }

    public String getPlat() {
        return plat;
    }

    public void setPlat(String plat) {
        this.plat = plat;
    }

    public String getPaytype() {
        return paytype;
    }

    public void setPaytype(String paytype) {
        this.paytype = paytype;
    }

    public String getRequestid() {
        return requestid;
    }

    public void setRequestid(String requestid) {
        this.requestid = requestid;
    }

    public String getUsetype() {
        return usetype;
    }

    public void setUsetype(String usetype) {
        this.usetype = usetype;
    }

    public String getSubusetype() {
        return subusetype;
    }

    public void setSubusetype(String subusetype) {
        this.subusetype = subusetype;
    }

    public String getSubpay() {
        return subpay;
    }

    public void setSubpay(String subpay) {
        this.subpay = subpay;
    }

    public thirdpartyinfo getThirdpartyinfo() {
        return thirdpartyinfo;
    }

    public void setThirdpartyinfo(thirdpartyinfo tpinfo) {
        this.thirdpartyinfo = tpinfo;
    }

    public SC31000303RequestParser.bankcardinfo getBankcardinfo() {
        return bankcardinfo;
    }

    public void setBankcardinfo(SC31000303RequestParser.bankcardinfo bankcardinfo) {
        this.bankcardinfo = bankcardinfo;
    }

    public SC31000303RequestParser.cardinfo getCardinfo() {
        return cardinfo;
    }

    public void setCardinfo(SC31000303RequestParser.cardinfo cardinfo) {
        this.cardinfo = cardinfo;
    }

    public class thirdpartyinfo{
        private String paymentwayid;
        private String typeid;
        private String subtypeid;
        private String typecode;
        private double amount;

        public String getPaymentwayid() {
            return paymentwayid;
        }

        public void setPaymentwayid(String paymentwayid) {
            this.paymentwayid = paymentwayid;
        }

        public String getTypeid() {
            return typeid;
        }

        public void setTypeid(String typeid) {
            this.typeid = typeid;
        }

        public String getSubtypeid() {
            return subtypeid;
        }

        public void setSubtypeid(String subtypeid) {
            this.subtypeid = subtypeid;
        }

        public String getTypecode() {
            return typecode;
        }

        public void setTypecode(String typecode) {
            this.typecode = typecode;
        }

        public double getAmount() {
            return amount;
        }

        public void setAmount(double amount) {
            this.amount = amount;
        }
    }

    public class bankcardinfo{
        private String paymentwayid;
        private String bindcardid;
        private String opttype;
        private String cardstatus;
        private String cardamount;

        public String getPaymentwayid() {
            return paymentwayid;
        }

        public void setPaymentwayid(String paymentwayid) {
            this.paymentwayid = paymentwayid;
        }

        public String getBindcardid() {
            return bindcardid;
        }

        public void setBindcardid(String bindcardid) {
            this.bindcardid = bindcardid;
        }

        public String getOpttype() {
            return opttype;
        }

        public void setOpttype(String opttype) {
            this.opttype = opttype;
        }

        public String getCardstatus() {
            return cardstatus;
        }

        public void setCardstatus(String cardstatus) {
            this.cardstatus = cardstatus;
        }

        public String getCardamount() {
            return cardamount;
        }

        public void setCardamount(String cardamount) {
            this.cardamount = cardamount;
        }
    }
    public class cardinfo{
        private String paymentwayid;
        private String bindcid;
        private String opttype;
        private String status;
        private String cardamount;
        private addinfo addinfo;

        public String getPaymentwayid() {
            return paymentwayid;
        }

        public void setPaymentwayid(String paymentwayid) {
            this.paymentwayid = paymentwayid;
        }

        public String getBindcid() {
            return bindcid;
        }

        public void setBindcid(String bindcid) {
            this.bindcid = bindcid;
        }

        public String getOpttype() {
            return opttype;
        }

        public void setOpttype(String opttype) {
            this.opttype = opttype;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getCardamount() {
            return cardamount;
        }

        public void setCardamount(String cardamount) {
            this.cardamount = cardamount;
        }

        public cardinfo.addinfo getAddinfo() {
            return addinfo;
        }

        public void setAddinfo(cardinfo.addinfo addinfo) {
            this.addinfo = addinfo;
        }

        public class addinfo{
            private String typeid;
            private String typemain;
            private String islast4;
            private String category;

            public String getTypeid() {
                return typeid;
            }

            public void setTypeid(String typeid) {
                this.typeid = typeid;
            }

            public String getTypemain() {
                return typemain;
            }

            public void setTypemain(String typemain) {
                this.typemain = typemain;
            }

            public String getIslast4() {
                return islast4;
            }

            public void setIslast4(String islast4) {
                this.islast4 = islast4;
            }

            public String getCategory() {
                return category;
            }

            public void setCategory(String category) {
                this.category = category;
            }
        }
    }

    public static void main(String[] args){
        String msg = "\"[[syscode=09,uid=M2839837974,logtype=paymentinfo,servicecode=31000303,guid=d3b2af1b-6e56-4176-8910-39b988ad04ea,serverIp=************,title=paymentSubmitSearchV3,calleetype=paymentSubmitSearchV3,maintype=CBU.31000303]]{\\\"serviceName\\\":\\\"paymentSubmitSearchV3\\\",\\\"requestHead\\\":{\\\"appid\\\":null,\\\"auth\\\":\\\"D66D5F936546F0162AF4E72EF1DE152E61FCA2760220AD984C3EB806F42574B6\\\",\\\"cid\\\":\\\"09031035410768207631\\\",\\\"ctok\\\":\\\"\\\",\\\"cver\\\":\\\"1.0\\\",\\\"extension\\\":[{\\\"name\\\":\\\"**d\\\",\\\"value\\\":\\\"wx0e6ed4f51db9d078\\\"},{\\\"name\\\":\\\"**e\\\",\\\"value\\\":\\\"1011\\\"},{\\\"name\\\":\\\"**h\\\",\\\"value\\\":\\\"D66D5F936546F0162AF4E72EF1DE152E61FCA2760220AD984C3EB806F42574B6\\\"},{\\\"name\\\":\\\"**e\\\",\\\"value\\\":\\\"MemberLogin\\\"},{\\\"name\\\":\\\"**d\\\",\\\"value\\\":\\\"M2839837974\\\"},{\\\"name\\\":\\\"**h\\\",\\\"value\\\":\\\"true\\\"},{\\\"name\\\":\\\"**w\\\",\\\"value\\\":\\\"true\\\"}],\\\"lang\\\":\\\"01\\\",\\\"pauth\\\":null,\\\"sauth\\\":\\\"\\\",\\\"sid\\\":\\\"8888\\\",\\\"syscode\\\":\\\"09\\\"},\\\"request\\\":{\\\"actcount\\\":0,\\\"actkey\\\":null,\\\"attach\\\":null,\\\"bustype\\\":7,\\\"cardinfo\\\":null,\\\"cashinfo\\\":null,\\\"cdinfo\\\":null,\\\"extend\\\":\\\"wx0e6ed4f51db9d078\\\",\\\"extendparam\\\":null,\\\"fncexpayway\\\":null,\\\"forcardcharg\\\":0,\\\"forcardfee\\\":0,\\\"geos\\\":null,\\\"guarantee\\\":null,\\\"head\\\":{\\\"appid\\\":null,\\\"auth\\\":\\\"D66D5F936546F0162AF4E72EF1DE152E61FCA2760220AD984C3EB806F42574B6\\\",\\\"cid\\\":\\\"09031035410768207631\\\",\\\"ctok\\\":\\\"\\\",\\\"cver\\\":\\\"1.0\\\",\\\"extension\\\":[{\\\"name\\\":\\\"**d\\\",\\\"value\\\":\\\"wx0e6ed4f51db9d078\\\"},{\\\"name\\\":\\\"**e\\\",\\\"value\\\":\\\"1011\\\"},{\\\"name\\\":\\\"**h\\\",\\\"value\\\":\\\"D66D5F936546F0162AF4E72EF1DE152E61FCA2760220AD984C3EB806F42574B6\\\"},{\\\"name\\\":\\\"**e\\\",\\\"value\\\":\\\"MemberLogin\\\"},{\\\"name\\\":\\\"**d\\\",\\\"value\\\":\\\"M2839837974\\\"},{\\\"name\\\":\\\"**h\\\",\\\"value\\\":\\\"true\\\"},{\\\"name\\\":\\\"**w\\\",\\\"value\\\":\\\"true\\\"}],\\\"lang\\\":\\\"01\\\",\\\"pauth\\\":null,\\\"sauth\\\":\\\"\\\",\\\"sid\\\":\\\"8888\\\",\\\"syscode\\\":\\\"09\\\"},\\\"insinfos\\\":[{\\\"insamount\\\":\\\"0\\\",\\\"inscurrency\\\":\\\"CNY\\\",\\\"provider\\\":\\\"1\\\"}],\\\"lastpaytm\\\":null,\\\"myaccountinfo\\\":null,\\\"oinfo\\\":{\\\"autoalybil\\\":true,\\\"currency\\\":\\\"CNY\\\",\\\"extno\\\":\\\"\\\",\\\"notify\\\":null,\\\"oamount\\\":16,\\\"odesc\\\":\\\"寒山寺\\\",\\\"oid\\\":**********,\\\"oidex\\\":**********,\\\"partdiscamount\\\":0,\\\"recall\\\":\\\"SOA20:http://orderpaymentsvc.ttd.ctripcorp.com/api/json/billPaymentCallback\\\"},\\\"opadbitmp\\\":0,\\\"opbitmap\\\":null,\\\"openid\\\":null,\\\"opttype\\\":1,\\\"orderlatestavailbletime\\\":null,\\\"passport\\\":null,\\\"payrestrict\\\":{\\\"blacklist\\\":null,\\\"cardnumseglist\\\":null,\\\"defaultpaytype\\\":0,\\\"discblacklist\\\":null,\\\"discountidlist\\\":null,\\\"paytypelist\\\":4,\\\"restrictbit\\\":0,\\\"senamelist\\\":null,\\\"subpaytypelist\\\":256,\\\"thirdrestrictlist\\\":null,\\\"whitelist\\\":null},\\\"paytoken\\\":null,\\\"paytype\\\":4,\\\"plat\\\":5,\\\"requestid\\\":\\\"78a3ef26ae6f469bab6772b80ff21263\\\",\\\"rmstoken\\\":null,\\\"rvcode\\\":null,\\\"scenarioinfo\\\":null,\\\"sdiscinfo\\\":null,\\\"seqid\\\":null,\\\"sourceinfo\\\":{\\\"accept\\\":null,\\\"allianceid\\\":\\\"262684\\\",\\\"searchmap\\\":0,\\\"sid\\\":\\\"711465\\\",\\\"stype\\\":0,\\\"uagent\\\":null,\\\"userAgent\\\":null},\\\"statistic\\\":null,\\\"streamcontrbit\\\":0,\\\"stype\\\":0,\\\"subpay\\\":0,\\\"subusetype\\\":0,\\\"thirdpartyinfo\\\":{\\\"amount\\\":16,\\\"brandid\\\":\\\"WechatScanCode\\\",\\\"brandtype\\\":\\\"2\\\",\\\"channelid\\\":\\\"324\\\",\\\"chargemode\\\":0,\\\"code\\\":null,\\\"collectionid\\\":null,\\\"couponid\\\":null,\\\"exchag\\\":null,\\\"extend\\\":null,\\\"extendjson\\\":null,\\\"mktopenid\\\":\\\"848d0faf-7bdc-490f-8247-4f7a86a2998d\\\",\\\"paymentwayid\\\":\\\"WechatScanCode\\\",\\\"proid\\\":null,\\\"returnurl\\\":null,\\\"subtypeid\\\":4,\\\"thirdcardnum\\\":null,\\\"thirdfee\\\":0,\\\"typecode\\\":\\\"\\\",\\\"typeid\\\":0},\\\"tktinfo\\\":null,\\\"touchpay\\\":null,\\\"uisflag\\\":0,\\\"userauthinfolist\\\":null,\\\"usetype\\\":1,\\\"ver\\\":710000,\\\"walletpayinfo\\\":null,\\\"withholdaccount\\\":null},\\\"response\\\":{\\\"bilno\\\":\\\"****************\\\",\\\"cardinfoid\\\":0,\\\"dbgmsg\\\":\\\"当前三方不需GetPayResult\\\",\\\"discamount\\\":0,\\\"fnccouponresult\\\":null,\\\"identityver\\\":4,\\\"insdetail\\\":null,\\\"nativewechaturl\\\":null,\\\"oid\\\":0,\\\"oidex\\\":**********,\\\"orderlefttime\\\":0,\\\"pdiscinfos\\\":null,\\\"pwdpayguideinterval\\\":null,\\\"rc\\\":0,\\\"realnameguideinterval\\\":null,\\\"refid\\\":null,\\\"responseStatus\\\":null,\\\"riskcode\\\":\\\"0\\\",\\\"rmsg\\\":null,\\\"seqid\\\":\\\"190722061827836lq84\\\",\\\"sphone\\\":null,\\\"streamcontrbit\\\":2,\\\"subcode\\\":null,\\\"thirdpartyinfo\\\":{\\\"referenceno\\\":\\\"20190722141828**********c1cc0b09\\\",\\\"sig\\\":\\\"{\\\\\\\"timeStamp\\\\\\\":\\\\\\\"**********\\\\\\\",\\\\\\\"package\\\\\\\":\\\\\\\"prepay_id=wx22141828679447dd16a6a6831613147400\\\\\\\",\\\\\\\"paySign\\\\\\\":\\\\\\\"D5983D96A2AA78A71CE225D6FA09756835B676F6D4243AC0F840DA348D2707A9\\\\\\\",\\\\\\\"appId\\\\\\\":\\\\\\\"wx0e6ed4f51db9d078\\\\\\\",\\\\\\\"signType\\\\\\\":\\\\\\\"HMAC-SHA256\\\\\\\",\\\\\\\"nonceStr\\\\\\\":\\\\\\\"99234c0224fb49f59aaaee25847aebbe\\\\\\\"}\\\",\\\"type\\\":0},\\\"threedsinfos\\\":null,\\\"threedsrefid\\\":null,\\\"thresholdtime\\\":0,\\\"weixindaifuurl\\\":null},\\\"beginTime\\\":\\\"2019-07-22 14:18:27.827\\\",\\\"endTime\\\":\\\"2019-07-22 14:18:28.821\\\",\\\"timeSpan\\\":994,\\\"appId\\\":null}\"";
        String bodyStr = MessageParser.parseBody(msg);
        TraceModel model = null;
        try {
            model = JSON.parseObject(bodyStr, TraceModel.class);
            SC31000303RequestParser ret = JSON.parseObject(model.getRequest().toString(), SC31000303RequestParser.class);
            System.out.println(ret.getThirdpartyinfo().getPaymentwayid());
        } catch (Exception e) {

        }

    }
}
