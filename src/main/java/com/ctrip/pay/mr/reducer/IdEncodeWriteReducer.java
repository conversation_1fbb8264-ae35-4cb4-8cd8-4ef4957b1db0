package com.ctrip.pay.mr.reducer;

import com.ctrip.pay.mr.Entity.BatchEncryptDto;
import com.ctrip.pay.mr.Entity.BatchEncryptRequestType;
import com.ctrip.pay.mr.Entity.EncryptResultDto;
import com.ctrip.pay.mr.map.CtripEncrypt;
import com.ctrip.pay.mr.utils.MRUtils;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class IdEncodeWriteReducer extends Reducer<Text, OrcValue, NullWritable, OrcStruct> {
    private String schema = "";
    private String inputSchema = "";
    private int BATCH_SIZE = 99;
    private TypeDescription inputTypeDescription;
    private OrcStruct pair = null;
    private BatchEncryptRequestType requestType;
    private List<BatchEncryptDto> param;

    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        schema= context.getConfiguration().get("orc.mapred.output.schema");
        inputSchema= context.getConfiguration().get("orc.mapred.map.output.value.schema");
        inputTypeDescription=TypeDescription.fromString(inputSchema);
        TypeDescription typeDescription=TypeDescription.fromString(schema);
        pair = (OrcStruct) OrcStruct.createValue(typeDescription);
    }

    @Override
    protected void reduce(Text key, Iterable<OrcValue> values, Context context) throws IOException, InterruptedException {
        param = new ArrayList<>();
        requestType = new BatchEncryptRequestType();
        List<OrcStruct> list=new ArrayList<>();
        try {
            for (OrcValue val : values) {
                OrcStruct struct = MRUtils.newOrcStruct(val, inputTypeDescription);
                list.add(struct);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        System.out.println("all count:"+list.size()+" segment:"+(list.size()/BATCH_SIZE+1));

        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(context.getConfiguration(), schema);

        for (int seg=0; seg<=list.size()/BATCH_SIZE;seg++) {
            for(int sub=seg*BATCH_SIZE; sub<(seg+1)*BATCH_SIZE && sub<list.size(); sub++) {
                OrcStruct val = list.get(sub);

                BatchEncryptDto m = new BatchEncryptDto();
                m.setId(val.getFieldValue(0).toString());
                m.setType(val.getFieldValue(1).toString());// 证件类型
                m.setKey(val.getFieldValue(2).toString());
                param.add(m);
            }
            requestType.setInfos(param);
            List<EncryptResultDto> results = null;
            try {
                results = CtripEncrypt.encode(requestType);
            }catch (Exception e){
                System.out.println("调用接口失败！");
            }
            String id="";
            if(results!=null&&results.size()>0) {
                for (int i=0;i<results.size();i++) {
                    if(results.get(i).getRawKey().getKey().equals(param.get(i).getKey())){
                        id = param.get(i).getId().toString();
                    }

                    pair.setFieldValue(0, new Text(id));
                    pair.setFieldValue(1, new Text(results.get(i).getRawKey().getKey()));
                    pair.setFieldValue(2, new Text(results.get(i).getResult()));
                    pair.setFieldValue(3, new Text(results.get(i).getStatusCode()));
                    context.write(NullWritable.get(), pair);
                }
            }else {
                for(int i=0;i<param.size();i++){
                    pair.setFieldValue(0, new Text(param.get(i).getType()));
                    pair.setFieldValue(1, new Text(param.get(i).getKey()));
                    pair.setFieldValue(2, new Text(param.get(i).getKey()));
                    pair.setFieldValue(3, new Text("ret is null"));
                    context.write(NullWritable.get(), pair);
                }
                System.out.println("emit anormal:"+param.size());
            }
            param.clear();
        }
    }
}
