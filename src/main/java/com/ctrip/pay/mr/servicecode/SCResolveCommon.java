package com.ctrip.pay.mr.servicecode;

import com.alibaba.fastjson.JSON;
import com.ctrip.pay.mr.map.ComposedKey;
import com.ctrip.pay.mr.reducer.OrcWriteCommonReducer;
import com.ctrip.pay.mr.servicecode.serialize.MessageParser;
import com.ctrip.pay.mr.servicecode.serialize.TagParser;
import com.ctrip.pay.mr.servicecode.serialize.TraceModel;
import com.ctrip.pay.mr.utils.FileUtils;
import com.ctrip.pay.mr.utils.MRUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.io.compress.CompressionCodec;
import org.apache.hadoop.io.compress.SnappyCodec;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.mapreduce.lib.input.FileSplit;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.LazyOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.hadoop.util.GenericOptionsParser;
import org.apache.hadoop.util.Tool;
import org.apache.hadoop.util.ToolRunner;
import org.apache.orc.OrcConf;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapreduce.OrcOutputFormat;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public class SCResolveCommon extends Configured implements Tool {
    public static String schemaPath = "ORCDescription/servicecode.properties";
    private static String outputBasePath = FileUtils.HIVE_BASE_PATH_INPUT_TMP;

    public static class SCCommonMapper
            extends Mapper<LongWritable, Text, ComposedKey, Text> {
        int count = 0;
        private ComposedKey outKey = new ComposedKey();

        @Override
        protected void setup(Context context) throws IOException, InterruptedException {

        }

        @Override
        protected void map(LongWritable key, Text value, Context context) throws IOException, InterruptedException {
            String msg = value.toString().trim();
            if (msg.length() < 20) { //异常行
                return;
            }
            //解析tag
            Map<String, String> tagsMap = TagParser.parseTags(msg);
            //解析msg
            String bodyStr = MessageParser.parseBody(msg);
            TraceModel model = null;
            try {
                model = JSON.parseObject(bodyStr, TraceModel.class);
            } catch (Exception e) {
            }
            //找不到orderid,uid,servicecode
            if (!tagsMap.containsKey("orderid") || !tagsMap.containsKey("uid") || !tagsMap.containsKey("servicecode") || model == null) {
                return;
            }
            String filename = ((FileSplit) context.getInputSplit()).getPath().toString();
            String[] days = filename.split("hourly/")[1].split("/");
            String day = (new StringBuilder(days[0]).append("-").append(days[1]).append("-").append(days[2])).toString();
            outKey.setServicecode(tagsMap.get("servicecode"));
            outKey.setUid(tagsMap.get("uid"));
            outKey.setDay(day);
            if (count++ < 3) {
                if (msg.length() < 2000) {
                    System.out.println("mapvalue:" + msg.toString());
                } else {
                    System.out.println("mapvalue:" + msg.toString().substring(1, 2000));
                }
            }
            //测试方便只用
            //if (count > 1000) return;
            context.write(outKey, new Text(msg));
        }
    }


    @Override
    public int run(String[] otherArgs) throws Exception {
        Configuration conf = getConf();
        if (otherArgs.length != 3) {
            System.out.println("argument is wrong,three argument is needed");
            return 0;
        }
        String schema = MRUtils.acquireRawSchema(schemaPath);
        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(conf, schema);
        conf.set("orc.mapred.output.schema", schema);
        conf.set("orc.mapred.map.output.value.schema", schema);
        conf.set("mapreduce.job.split.metainfo.maxsize", "150000000");
        conf.set("dfs.socket.timeout", "180000");
        conf.set("dfs.datanode.socket.write.timeout", "180000");
        conf.set("dfs.client.socket-timeout", "180000");
        conf.set("mapred.child.java.opts", "10240");
        conf.set("mapreduce.map.memory.mb", "10240");
        conf.set("mapreduce.reduce.memory.mb", "10240");
        conf.set("mapreduce.map.java.opts", "-Xmx10000m -XX:-UseGCOverheadLimit");
        conf.set("mapreduce.reduce.java.opts", "-Xmx9000m");
        conf.setStrings("mapreduce.reduce.shuffle.memory.limit.percent", "0.15");
        //day outputbase
        System.out.println("start day:" + otherArgs[0]);
        System.out.println("end day:" + otherArgs[1]);
        System.out.println("end day:" + otherArgs[2]);
        outputBasePath = otherArgs[2].trim();
        conf.set("mapreduce.input.fileinputformat.split.maxsize", "268435456");
        conf.setBoolean("mapred.output.compress", true);
        conf.setBoolean("mapred.compress.map.output", true);
        conf.setClass("mapred.output.compression.codec", SnappyCodec.class, CompressionCodec.class);
        FileSystem fs = FileSystem.get(conf);
        Job job = Job.getInstance(conf, "CBU LOG EXTRACT COMMON");
        job.setJarByClass(SCResolveCommon.class);
        job.setMapperClass(SCCommonMapper.class);
        job.setMapOutputKeyClass(ComposedKey.class);
        job.setMapOutputValueClass(Text.class);
        job.getConfiguration().set("orc.mapred.value.type", "OrcValue");

        job.setReducerClass(OrcWriteCommonReducer.class);
        job.setOutputKeyClass(NullWritable.class);
        job.setOutputValueClass(OrcStruct.class);
        // job.setInputFormatClass(CombineTextInputFormat.class);

        job.setNumReduceTasks(300);
        //获取白名单以外的数据
        List<String> scList = FileUtils.getBlackListServiceCode();
        for (String item : scList) {
            List<String> partitionLists = FileUtils.generateServicecodeInput(item, otherArgs[0], otherArgs[1]);
            MRUtils.addInputDirs(partitionLists, fs, job); //测试，缩小数据集
        }
        fs.delete(new Path(outputBasePath), true);
        FileOutputFormat.setOutputPath(job, new Path(outputBasePath));

        /**
         *批量处理的servicecode名称
         */
        for (String output : scList) {
            MultipleOutputs.addNamedOutput(job, output, OrcOutputFormat.class, NullWritable.class, OrcStruct.class);
            String outputPath = FileUtils.getOutputDir(output, otherArgs[0]);
            System.out.println("start to delete output:" + outputPath);
            fs.delete(new Path(outputPath), true);

        }
        LazyOutputFormat.setOutputFormatClass(job, OrcOutputFormat.class);

        if (otherArgs[otherArgs.length - 1].equals("@")) {
            job.submit();
            return 0;
        } else {
            return job.waitForCompletion(true) ? 0 : 1;
        }
    }


    public static void main(String[] args) throws Exception {
        Configuration conf = new Configuration();
        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
        System.exit(ToolRunner.run(conf, new SCResolveCommon(), otherArgs));
    }

}
