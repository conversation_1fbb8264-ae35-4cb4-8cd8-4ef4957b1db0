package com.ctrip.pay.mr.utils;

import com.alibaba.fastjson.JSON;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;

import java.math.BigDecimal;

/**
 * Created by tgzhao on 2017/10/19.
 */
public class JsonObjectUtil {

    public static JsonObject getJsonObject(JsonObject obj, String memName) {
        if (obj == null) {
            return null;
        }
        if (!obj.get(memName).isJsonNull()) {
            return obj.getAsJsonObject(memName);
        }
        return null;
    }

    public static JsonArray getJsonArray(JsonObject obj, String memName) {
        if (obj == null) {
            return null;
        }
        if (isNotNull(obj.get(memName))) {
            return obj.getAsJsonArray(memName);
        }
        return null;
    }

    public static String getStrElement(JsonElement element, String memName) {
        try {
            if (isNotNull(element)) {
                JsonElement mem = element.getAsJsonObject().get(memName);
                if (isNotNull(mem)) {
                    return mem.getAsString();
                }
            }
        } catch (Exception e) {
        }
        return "";
    }

    public static int getIntElement(JsonElement element, String memName) {
        try {
            if (isNotNull(element)) {
                JsonElement mem = element.getAsJsonObject().get(memName);
                if (isNotNull(mem)) {
                    return mem.getAsInt();
                }
            }
        } catch (Exception e) {
        }
        return 0;
    }

    public static BigDecimal getDecimalElement(JsonElement element, String memName) {
        try {
            if (isNotNull(element)) {
                JsonElement mem = element.getAsJsonObject().get(memName);
                if (isNotNull(mem)) {
                    return mem.getAsBigDecimal();
                }
            }
        } catch (Exception e) {
        }

        return BigDecimal.ZERO;
    }

    public static String getStrObject(JsonObject obj, String memName) {
        try {
            if (obj != null) {
                JsonElement mem = obj.get(memName);
                if (isNotNull(mem)) {
                    return mem.getAsString();
                }
            }
        } catch (Exception e) {
        }

        return "";
    }

    public static int getIntObject(JsonObject obj, String memName) {
        try {
            if (obj != null) {
                JsonElement mem = obj.get(memName);
                if (isNotNull(mem)) {
                    return mem.getAsInt();
                }
            }
        } catch (Exception e) {
        }

        return -1;
    }


    public static Object getObject(String a) {
        Object result;
        try {
            result = JSON.parse(a);
        } catch (Exception e) {
            result = a;
        }
        return result;
    }


    private static boolean isNotNull(JsonElement element) {
        return element != null && element != JsonNull.INSTANCE;
    }
}
