package com.ctrip.pay.mr.reducer;

import com.ctrip.pay.mr.utils.MRUtils;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;

import java.io.IOException;

/**
 * @Author: ymfang
 * @Date: 2019/7/18 14:52
 */
public class UnionWriteMultioutputReducer
        extends Reducer<Text, OrcValue, NullWritable, OrcStruct> {
    private int count = 0;
    private String schema = "";
    private MultipleOutputs<NullWritable, OrcStruct> multipleOutputs;
    private int bucketNum = 20;

    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        schema = context.getConfiguration().get("orc.mapred.output.schema");
        multipleOutputs = new MultipleOutputs(context);
        TypeDescription typeDescription = TypeDescription.fromString(schema);
        typeDescription.createRowBatch(100);
    }

    @Override
    public void cleanup(Context context) throws IOException, InterruptedException {
        if (multipleOutputs != null) {
            multipleOutputs.close();
        }

    }

    @Override
    protected void reduce(Text key, Iterable<OrcValue> values, Context context) throws IOException, InterruptedException {

        Splitter splitter = Splitter.on(MRUtils.col_seperator).trimResults();
        String[] fields = Lists.newArrayList(splitter.split(key.toString())).toArray(new String[]{});
        if (fields == null || fields.length != 3) {
            System.out.println("fields's lenght is not equal 3 or is null.");
            return;
        }

        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(context.getConfiguration(), schema);

        for (OrcValue itm : values) {
            if ((count++) < 3) {
                System.out.println("InnerReducer:" + count + ": uid=" + key + "  value:" + itm.toString()+ " appid:" + fields[1]);

            }
            try {
                int index = Integer.parseInt(fields[1]) % bucketNum;
                multipleOutputs.write("paylog", NullWritable.get(), (OrcStruct) itm.value, "appid=" + fields[1] + "/");
            } catch (Exception e) {
                System.out.println(e.toString());
            }
        }


    }
}