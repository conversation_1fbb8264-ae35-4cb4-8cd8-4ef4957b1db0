package com.ctrip.pay.mr.inputformat;

import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.WritableComparable;
import org.apache.hadoop.mapreduce.RecordReader;
import org.apache.hadoop.mapreduce.TaskAttemptContext;
import org.apache.hadoop.mapreduce.lib.input.CombineFileRecordReader;
import org.apache.hadoop.mapreduce.lib.input.CombineFileSplit;

import java.io.IOException;

public class CombinedSmallFileRecordReader<V extends WritableComparable> extends CombineFileRecordReader<NullWritable, V> {
	public CombinedSmallFileRecordReader(CombineFileSplit split, TaskAttemptContext context, Class<? extends RecordReader<NullWritable, V>> rrClass) throws IOException {
		super(split, context, rrClass);
	}

	@Override
	protected boolean initNextRecordReader() {
		try{
			return super.initNextRecordReader();
		}
		catch (Throwable throwable){
			return false;
		}
	}



}
