package com.ctrip.pay.mr.utils;

import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;

import java.io.IOException;
import java.net.URI;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: ymfang
 * @Date: 2019/7/18 11:00
 */
public class FileUtilsbk {

    public static String HIVE_BASE_PATH_OUTPUT_ROUTE = "/user/biuser/warehouse/etl/Ods_PayDB.db/fpayclogrouterformat";
    public static String HIVE_BASE_PATH_OUTPUT = "/user/biuser/warehouse/etl/Ods_PayDB.db/fpayclogformat";
    public static String HIVE_BASE_PATH_INPUT = "/user/bifin/pay/servicecode/destination/pal_cbu_log_created";
    public static String HIVE_BASE_PATH_INPUT_ROUTE = "/user/bifin/pay/router/destination/fnc_log_back_100003993_created";
    public static String HIVE_BASE_PATH_INPUT_OLD="/user/bifin/pay/servicecode_old/destination/pal_cbulognet_created";
    public static String HIVE_BASE_PATH_INPUT_TMP = "/user/bifin/pay/servicecode_temp/";
    private static String whitelistName = "ORCDescription/blacklist.properties";
    public static String getOutputDir(String servicecode, String dt) {
        return String.format(HIVE_BASE_PATH_OUTPUT + "/servicecode=%s/dt=%s", servicecode, dt);
    }
    public static String getRouteOutputDir(String servicecode, String dt) {
        return String.format(HIVE_BASE_PATH_OUTPUT_ROUTE + "/appid=%s/dt=%s", servicecode, dt);
    }

    public static List<String> generateServicecodeInput(String servicecode, String startDate) {
        return generateServicecodeInput(servicecode, startDate, startDate);
    }

    public static List<String> generateServicecodeInput(String servicecode, String startDate, String endDate) {

        if (!StringUtils.isNotEmpty(servicecode) || !StringUtils.isNotEmpty(startDate) || !StringUtils.isNotEmpty(endDate)) {
            return new ArrayList<String>();
        }
        int rang = DateUtil.dateRangeCalc(startDate, endDate);//小时数

        Date date = Date.valueOf(startDate);
        List<String> dirs = DateUtil.dirs(date.getTime(), 1, rang,true); //两个月
        List<String> path = Lists.newArrayList();
        for (String replace : dirs) {
            String inPutPath = String.format(HIVE_BASE_PATH_INPUT + "/%s/hourly/%s/", new Object[]{servicecode, replace});
            path.add(inPutPath);
            inPutPath = String.format(HIVE_BASE_PATH_INPUT_OLD + "/%s/hourly/%s/", new Object[]{servicecode, replace});
            path.add(inPutPath);
        }
        return path;
    }

    public static List<String> generateRouteInput(String appid,String startDate, String endDate) {

        if (!StringUtils.isNotEmpty(startDate) || !StringUtils.isNotEmpty(endDate)) {
            return new ArrayList<String>();
        }
        int rang = DateUtil.dateRangeCalc(startDate, endDate);//小时数

        Date date = Date.valueOf(startDate);
        List<String> dirs = DateUtil.dirs(date.getTime(), 1, rang,true); //两个月
        List<String> path = Lists.newArrayList();
        for (String replace : dirs) {
            String inPutPath = String.format(HIVE_BASE_PATH_INPUT_ROUTE + "/%s/hourly/%s/", new Object[]{appid, replace});
            path.add(inPutPath);
            System.out.println("input:"+inPutPath);
        }
        return path;
    }


    /**
     * sample:/user/bifin/pay/servicecode/destination/pal_cbu_log_created/31000102/hourly/2019/07/17/21
     * 获取hdfs上所有的servicecode
     * @return
     * @throws IOException
     */
    public static List<String> getBlackListServiceCode() throws IOException {

        List<String> blackList = MRUtils.getBlacklist(whitelistName);

        List<String> scList = new ArrayList<>();
        // 创建Configuration对象
        Configuration conf = new Configuration();
        FileSystem fs = FileSystem.get(URI.create(HIVE_BASE_PATH_INPUT), conf);

        FileStatus[] status = fs.listStatus(new Path(HIVE_BASE_PATH_INPUT));

        for (FileStatus s : status) {
            String filePath = s.getPath().toString();
            String pattern = "pal_cbu_log_created/(\\d+)";
            Pattern r = Pattern.compile(pattern);
            String scName = "";
                    // 现在创建 matcher 对象
            Matcher m = r.matcher(filePath);
            if(m.find()) {
                scName=m.group(1);
            }
            if(!blackList.contains(scName)){
                scList.add(scName);
            }
        }
        return scList;

    }


    public static void main(String[] args) {
        List<String> dir = generateRouteInput("route", "2019-08-19","2019-08-19");
        for (String str : dir) {
          //  System.out.println(str);
        }

        System.out.println(getRouteOutputDir("12121212","2019-08-19"));

//        String filePath = "hdfs://ns/user/bifin/pay/servicecode/destination/pal_cbu_log_created/90200401";
//        String pattern = "pal_cbu_log_created/(\\d+)";
//        Pattern r = Pattern.compile(pattern);
//        String scName = "";
//        // 现在创建 matcher 对象
//        Matcher m = r.matcher(filePath);
//        if(m.find()) {
//           System.out.println(m.group(1));
//        }


    }
}
