package com.ctrip.pay.mr.servicecode;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.pay.mr.inputformat.CombinedOrcInputFormat;
import com.ctrip.pay.mr.reducer.CbuWriteMultioutputReducer;
import com.ctrip.pay.mr.servicecode.serialize.MessageParser;
import com.ctrip.pay.mr.servicecode.serialize.TagParser;
import com.ctrip.pay.mr.utils.DateUtil;
import com.ctrip.pay.mr.utils.FileUtils;
import com.ctrip.pay.mr.utils.JsonUtils;
import com.ctrip.pay.mr.utils.MRUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.io.compress.CompressionCodec;
import org.apache.hadoop.io.compress.SnappyCodec;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.LazyOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.hadoop.util.GenericOptionsParser;
import org.apache.hadoop.util.Tool;
import org.apache.hadoop.util.ToolRunner;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;
import org.apache.orc.mapreduce.OrcOutputFormat;

import java.io.IOException;
import java.util.*;

public class FPaymentRoutePre100044010 extends Configured implements Tool {
    public static String schemaPath = "ORCDescription/ods_fpayment_route_pre.properties";


    public static class CbuLogMapper
            extends Mapper<NullWritable, OrcStruct, Text, OrcValue> {
        private Text outKey = new Text();
        private OrcValue outValue = new OrcValue();
        //对应orc表数据结构
        private OrcStruct pair = null;
        private String schema = "";
        //对应hive表字段
        private List<String> fieldList = new ArrayList<>();


        @Override
        protected void setup(Context context) throws IOException, InterruptedException {
            schema = context.getConfiguration().get("orc.mapred.output.schema");
            System.out.println("start to step up schema:" + schema);
            fieldList = MRUtils.initalFields(schemaPath);
            TypeDescription typeDescription = TypeDescription.fromString(schema);
            typeDescription.createRowBatch(100);
            pair = (OrcStruct) OrcStruct.createValue(typeDescription);
        }

        @Override
        protected void map(NullWritable key, OrcStruct value, Context context) throws IOException, InterruptedException {

            //从[[开始截取字符串并移除字符串中的特殊字符；
            String msg = MessageParser.parseMsg(value.getFieldValue(0).toString());
            //解析tag转换成map；
            Map<String, String> tagsMap = TagParser.parseTags(msg);
            // 获取message字符串；
            String messageStr = MessageParser.parseBodyForPayDispath(msg);
            if (tagsMap.keySet().size() == 0) {
                return;
            }
            //只解析tag中包含orderid和uid的数据；
            if (!(tagsMap.containsKey("servicecode") || tagsMap.containsKey("uid"))) {
                return;
            }

            //根据条件获取支付提交主日志；
            String uid = tagsMap.getOrDefault("uid", "");
            String serviceCode = tagsMap.getOrDefault("servicecode", "");
            if (!serviceCode.matches("(.*)[0-9]+(.*)")) {
                System.out.println("abandom:" + uid + "_" + serviceCode);
                return;
            }

            String paymentAppId = tagsMap.getOrDefault("paymentappid", "");
            String transType = tagsMap.getOrDefault("transtype", "");
            String logTime = tagsMap.getOrDefault("logtime", "");
            Long dateLong = DateUtil.getDateLong(DateUtil.getDateLong(logTime));


//            boolean isNeedData = paymentAppId.equals("100044010") && serviceCode.equals("31100303")
//                    && (transType.equals("paymentroutefromfront") || transType.equals("paymentroutefromfront303"));
            boolean isNeedData = true;
            //解析需要的数据；
            if (isNeedData) {
                //不能用_作为分隔符，因为部分uid就是以_开头的,dateLong,打散key；
                outKey.set(uid + MRUtils.col_seperator + serviceCode + MRUtils.col_seperator + dateLong);
                outValue.value = givePairValue(msg, tagsMap, messageStr, fieldList, pair);
            }
            //解析其他数据；
            if (!isNeedData) {
                outKey.set(uid + MRUtils.col_seperator + serviceCode + MRUtils.col_seperator + dateLong);
                outValue.value = givePairValue(msg, tagsMap, messageStr, fieldList, pair);
            }
            context.write(outKey, outValue);
        }

        //解析需要使用的数据；
        public static OrcStruct givePairValue(String oriStr, Map<String, String> tagsMap, String messageStr, List<String> fieldList, OrcStruct pair) {

            JSONObject jsonObject = null;

            try {
                jsonObject = JSONObject.parseObject(messageStr);
            } catch (Exception e) {
                System.out.println(e.toString());
            }

            JSONObject finalJsonObject = jsonObject;
            fieldList.forEach(field -> {
                int index = fieldList.indexOf(field);
                String value = "";
                switch (field) {
                    case "uid":
                        if (tagsMap != null) {
                            value = tagsMap.getOrDefault("uid", "");
                        } else {
                            value = "";
                        }
                        break;

                    //plat -> source
                    case "source":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("plat"))
                                .orElse("");
                        break;

                    //根据syscode转换plat
                    case "plat":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("syscode"))
                                .map(x -> {
                                    String result = x;
                                    switch (x) {
                                        case "12":
                                            result = "ios";
                                            break;
                                        case "32":
                                            result = "android";
                                            break;
                                        case "09":
                                            result = "h5";
                                            break;
                                    }
                                    return result;
                                })
                                .orElse("");
                        break;

                    case "syscode":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("syscode"))
                                .orElse("");
                        break;
                    case "service_version":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("serviceversion"))
                                .orElse("");
                        break;
                    case "result":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("result"))
                                .orElse("");
                        break;
                    case "tokenid":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("tokenid"))
                                .orElse("");
                        break;
                    case "submit_paymentwayid":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("submit_paymentwayid"))
                                .orElse("");
                        break;
                    case "submit_bankname":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("submit_bankname"))
                                .orElse("");
                        break;
                    case "paymentwayid":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("paymentwayid"))
                                .orElse("");
                        break;
                    case "creditcard_paymentwayid":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("creditcard_paymentwayid"))
                                .orElse("");
                        break;
                    case "is_new_card_submit":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("is_new_card_submit"))
                                .orElse("");
                        break;
                    case "is_submit_from_prepose_cashier":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("is_submit_from_prepose_cashier"))
                                .orElse("");
                        break;
                    case "request_time":
//                        value = Optional.ofNullable(finalJsonObject)
//                                .map(x -> x.getString("requesttime"))
//                                .orElse("");
                        String logTime = tagsMap.getOrDefault("logtime", "");
                        if (logTime.length() > 10) {
                            String date = Optional.ofNullable(logTime).map(x -> x.substring(0, 10)).orElse("");
                            String hhmmss = Optional.ofNullable(logTime).map(x -> x.substring(10)).orElse("");
                            value = date.concat(" ").concat(hhmmss);
                        } else {
                            value = logTime;
                        }
                        break;
                    case "extmap":
                        HashMap<String, String> map = new HashMap<>();
                        String billno = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("billno"))
                                .orElse("");
                        map.put("billno", billno);

                        value = map.toString();
                        break;
                    case "tag":
                        value = Optional.ofNullable(tagsMap).map(JsonUtils::toJson).map(x -> x.replaceAll(" ", "")).orElse("");
                        break;
                    case "message":
                        value = messageStr;
                        break;
                    case "message_raw":
                        value = oriStr;
                        break;
                    case "resultmessage":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("resultmessage"))
                                .orElse("");
                        break;

                    case "order_id":
                        value = tagsMap.getOrDefault("orderid", "");
                        break;

                    case "submit_promotionid":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("submit_promotionid"))
                                .orElse("");
                        break;

                    case "submit_discounttitle":
                        value = Optional.ofNullable(finalJsonObject)
                                .map(x -> x.getString("submit_discounttitle"))
                                .orElse("");
                        break;
                    case "paytype":
                        value = tagsMap.getOrDefault("payscence", "");
                        break;

                }
                pair.setFieldValue(index, new Text(value));
            });
            return pair;
        }

    }


    @Override
    public int run(String[] otherArgs) throws Exception {
        Configuration conf = getConf();
        if (otherArgs.length != 1) {
            System.out.println("argument is wrong,single argument is required");
            return 0;
        }
        String schema = MRUtils.acquireRawSchema(schemaPath);
        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(conf, schema);
        OrcConf.MAPRED_SHUFFLE_VALUE_SCHEMA.setString(conf, schema);

        conf.set("mapreduce.input.fileinputformat.split.maxsize", "134217728");
        conf.set("mapreduce.task.io.sort.mb", "200");
        conf.setBoolean("mapreduce.map.output.compress", true);
        conf.setClass("mapreduce.map.output.compress.code", SnappyCodec.class, CompressionCodec.class);
        //map内存调大
        conf.set("mapreduce.map.memory.mb", "8096");
        conf.set("mapreduce.map.java.opts", "-Xmx6000m");
        //reduce内存调大
        conf.set("mapreduce.reduce.memory.mb", "8096");
        conf.set("mapreduce.reduce.java.opts", "-Xmx6000m");
        conf.set("mapreduce.reduce.shuffle.memory.limit.percent", "0.15");
//        conf.set("mapreduce.reduce.shuffle.merge.percent", "0.2");
        conf.set("mapreduce.reduce.speculative", "false");

        conf.setBoolean(OrcOutputFormat.SKIP_TEMP_DIRECTORY, true);
        conf.set("mapreduce.map.output.value.class", "org.apache.orc.mapred.OrcValue");

        System.out.println("param1:" + otherArgs[0]);
        FileSystem fs = FileSystem.get(conf);
        Job job = Job.getInstance(conf, "ods_paydb.ods_fpayment_route_pre_100044010 etl");
        job.setReduceSpeculativeExecution(false);
        job.setJarByClass(FPaymentRoutePre100044010.class);
        job.setMapperClass(CbuLogMapper.class);
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(OrcValue.class);
        job.setInputFormatClass(CombinedOrcInputFormat.class);
        job.setReducerClass(CbuWriteMultioutputReducer.class);
        job.setOutputKeyClass(NullWritable.class);
        job.setOutputValueClass(OrcStruct.class);

        job.setNumReduceTasks(1000);
        String root = "/user/bifin/pay/pay_log_raw_orc/destination/fnc_log_created/100044010";
        List<String> partitionLists = FileUtils.generateRdInputPrefix(root, otherArgs[0], otherArgs[0]);
        Collections.shuffle(partitionLists);
        MRUtils.addInputDirs(partitionLists, fs, job);

        System.out.println("输入文件个数" + partitionLists.size());
        if (partitionLists.size() > 0) {
            System.out.println("输入文件格式" + partitionLists.get(0));
        } else {
            System.out.println("输入文件数为0");
            return -1;
        }
        String outputPath = String.format("/user/biuser/warehouse/etl/Ods_PayDB.db/ods_fpayment_route_pre_100044010" + "/dt=%s/", otherArgs[0]);
        job.getConfiguration().set("cbu.path", outputPath);

        FileOutputFormat.setOutputPath(job, new Path(outputPath));
        MultipleOutputs.addNamedOutput(job, "cbulog", OrcOutputFormat.class, NullWritable.class, OrcStruct.class);
        LazyOutputFormat.setOutputFormatClass(job, OrcOutputFormat.class);
        System.out.println("start to delete output:" + outputPath);

        boolean fileDelete = fs.delete(new Path(outputPath), true);
        if (fileDelete) {
            System.out.println("delete output:" + outputPath + " successful");
        } else {
            System.out.println("delete output:" + outputPath + " failed");
        }
        if (otherArgs[otherArgs.length - 1].equals("@")) {
            job.submit();
            return 0;
        } else {
            return job.waitForCompletion(true) ? 0 : 1;
        }
    }


    public static void main(String[] args) throws Exception {
        Configuration conf = new Configuration();
        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
        System.exit(ToolRunner.run(conf, new FPaymentRoutePre100044010(), otherArgs));
    }

}
