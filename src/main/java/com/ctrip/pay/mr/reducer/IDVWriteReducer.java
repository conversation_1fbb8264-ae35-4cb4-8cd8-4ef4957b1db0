package com.ctrip.pay.mr.reducer;

import com.ctrip.pay.mr.map.IDVerify;
import com.ctrip.pay.mr.servicecode.serialize.BatchIdVerificationDto;
import com.ctrip.pay.mr.servicecode.serialize.IdVerificationDto;
import com.ctrip.pay.mr.utils.MRUtils;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class IDVWriteReducer extends Reducer<Text, OrcValue,NullWritable, OrcStruct> {
    private String schema="";
    private String inputschema="";

    private static  int BATCH_SIZE=49;
    private OrcStruct pair = null;
    private List<IdVerificationDto> param;
    private TypeDescription inputtype;

    @Override
    protected void setup(Context context) throws IOException, InterruptedException {

        schema = context.getConfiguration().get("orc.mapred.output.schema");
        inputschema = context.getConfiguration().get("orc.mapred.map.output.value.schema");
        inputtype=TypeDescription.fromString(inputschema);
        TypeDescription typeDescription=TypeDescription.fromString(schema);
        pair = (OrcStruct) OrcStruct.createValue(typeDescription);
    }

    //the framework calls this method for each <key, (list of values)> pair in the grouped inputs. Output values must be of the same type as input values. Input keys must not be altered. The framework will reuse the key and value objects that are passed into the reduce, therefore the application should clone the objects they want to keep a copy of.
    @Override
    protected void reduce(Text key, Iterable<OrcValue> values, Context context) throws IOException, InterruptedException {
        //
        List<OrcStruct> list = new ArrayList<>();
        for(OrcValue val:values){
            OrcStruct struct = MRUtils.newOrcStruct(val,inputtype);
            list.add(struct);

        }
        System.out.println("all cnt:"+list.size()+" segment:"+(list.size()/BATCH_SIZE+1));
        param=new ArrayList<>();

        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(context.getConfiguration(), schema);

        for(int seg=0;seg<=list.size()/BATCH_SIZE;seg++){

            //组装好参数，一次emit 50个参数
            for(int sub=seg*BATCH_SIZE;sub<(seg+1)*BATCH_SIZE && sub<list.size();sub++){

                OrcStruct val = list.get(sub);

                IdVerificationDto m=new IdVerificationDto();
                m.setUid(val.getFieldValue(0).toString());
                m.setIdName(val.getFieldValue(1).toString());
                m.setIdNumber(val.getFieldValue(2).toString());
                param.add(m);
            }
            List<BatchIdVerificationDto> results = null;
            try {
//                results = IDVerify.check(param);
            }catch (Exception e){
                System.out.println("接口调用失败");
            }

            if(results!=null&&results.size()>0){
                String idnoen = "";
                for (int i = 0; i < results.size(); i++) {
                    pair.setFieldValue(0, new Text(param.get(i).getUid()));
                    pair.setFieldValue(1, new Text(results.get(i).getIdName()));
                    if (results.get(i).getIdName().equals(param.get(i).getIdName())) {
                        idnoen = param.get(i).getIdNumber();
                    } else {
                        idnoen = results.get(i).getIdNumber();
                    }
                    pair.setFieldValue(2, new Text(idnoen));
                    pair.setFieldValue(3, new Text(results.get(i).getVerifyCode()));
                    pair.setFieldValue(4, new Text(results.get(i).getVerifyMsg()));
                    pair.setFieldValue(5, new Text(results.get(i).getVerifyServiceType()));
                    context.write(NullWritable.get(), pair);
                }
            }else{
                for(int i=0;i<param.size();i++) {
                    pair.setFieldValue(0, new Text(param.get(i).getUid()));
                    pair.setFieldValue(1, new Text(param.get(i).getIdName()));
                    pair.setFieldValue(2, new Text(param.get(i).getIdNumber()));
                    for(int j=3;j<pair.getNumFields();j++){
                        pair.setFieldValue(j, new Text(""));
                    }
                    pair.setFieldValue(4, new Text("ret is null or not a List<BatchIdVerificationDto"));
                    context.write(NullWritable.get(), pair);
                }
                System.out.println("emit anormal:"+param.size());

            }
            param.clear();
        }



    }
}
