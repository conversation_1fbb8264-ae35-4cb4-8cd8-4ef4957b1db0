package com.ctrip.pay.mr.servicecode;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.pay.mr.servicecode.serialize.TraceModel;
import com.ctrip.pay.mr.utils.*;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.util.GenericOptionsParser;
import org.apache.hadoop.util.Tool;
import org.apache.hadoop.util.ToolRunner;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcMap;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;
import org.apache.orc.mapreduce.OrcOutputFormat;

import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class APPID100006883Resolve extends Configured implements Tool {
    public static String schemaPath = "ORCDescription/appid.properties";
    public static class APPID100006883Mapper
            extends Mapper<LongWritable, Text, Text, OrcValue> {
        int count = 0;
        private Text outKey = new Text();
        private OrcValue outValue = new OrcValue();
        //对应hive表字段
        private List<String> fieldList = new ArrayList<>();

        //对应orc表数据结构
        private OrcStruct pair = null;
        private String schema = "";

        @Override
        protected void setup(Context context) throws IOException, InterruptedException {
            fieldList = MRUtils.initalFields(schemaPath);
            schema = MRUtils.acquireRawSchema(schemaPath);
            pair = (OrcStruct) OrcStruct.createValue(TypeDescription.fromString(schema));
        }

        @Override
        protected void map(LongWritable key, Text value, Context context) throws IOException, InterruptedException {
            String msg = value.toString().trim();
            if (msg.length() < 20) { //异常行
                return;
            }

            //解析tag
            Map<String,String> tags= new HashMap<>();

            JSONObject m = JSON.parseObject(msg);
            String payload;
            TraceModel model=new TraceModel();

            try {
                payload = m.getJSONObject("stored").getString("payload");
                if(payload==null || payload.length() == 0){
                    payload = m.getJSONObject("stored").getString("message");
                    if(payload==null || payload.length() == 0) {
                        return;
                    }
                }
            } catch (Exception e) {
                return;
            }

            try {
                model = JSON.parseObject(payload, TraceModel.class);
            } catch (Exception e) {
                return;
            }

            tags.put("uid", m.getString("uid"));
            tags.put("orderid", m.getString("orderid"));
            tags.put("title", m.getString("title"));
            tags.put("plat", m.getString("plat"));
            tags.put("syscode", m.getString("syscode"));
            tags.put("logtype", m.getString("logtype"));
            tags.put("servicecode", m.getString("servicecode"));
            tags.put("merchantid", m.getString("merchantid"));
            tags.put("bustype", m.getString("bustype"));
            tags.put("serviceversion", m.getString("serviceversion"));
            tags.put("guid", m.getString("guid"));

            if(! (tags.containsKey("orderid") || tags.containsKey("uid"))){
                return;
            }

            try{
                JSONObject request = JSON.parseObject(model.getRequest().toString());
                model.setRequest(request);

                JSONObject respnse = JSON.parseObject(model.getResponse().toString());
                model.setResponse(respnse);

                JSONObject requestHead = JSON.parseObject(model.getRequestHead().toString());
                model.setRequestHead(requestHead);

                //往Tags中添加OrderAmount
                if (!tags.containsKey("OrderAmount") ) {
                    try {
                        if (request != null) {
                            JSONObject oinfo = request.getJSONObject("oinfo");
                            if (oinfo != null) {
                                tags.put("OrderAmount", oinfo.get("oamount").toString());
                            } else {
                                oinfo = request.getJSONObject("OrderInfo");
                                if (oinfo != null) {
                                    tags.put("OrderAmount", oinfo.get("OrderAmount").toString());
                                }
                            }
                        }
                    }catch (Exception e){

                    }
                }

            }catch (Exception ex){

            }


            if (model == null) {
                model = new TraceModel();
            }

            //计算出plat和resultcode
            if (!tags.containsKey("plat")) {
                tags.put("plat", model.getPlat() + "");
            }

            tags.put("resultcode", model.getResultcode() + "");

            String orderid = "";
            String bustype ="";
            try {
                orderid = model.getRequestElement("orderid");
                bustype = model.getRequestElement("bustype");

                if (!tags.containsKey("orderid") && orderid != null) {
                    tags.put("orderid", orderid);
                }

                if (!tags.containsKey("bustype") && bustype != null) {
                    tags.put("bustype", bustype);
                }

                tags.put("servicename", model.getServiceName());
                tags.put("requesttime", DateUtil.format(model.getBeginTime()));
            } catch (Exception e) {

            }

            String uid = "";
            if (tags.containsKey("uid")) {
                uid = tags.get("uid");
            }
            if(uid==null){
                uid="";
            }

            outKey.set(uid);
            OrcConf.MAPRED_SHUFFLE_VALUE_SCHEMA.setString(context.getConfiguration(), schema);
            OrcConf.MAPRED_OUTPUT_SCHEMA.setString(context.getConfiguration(), schema);

            //赋值uid、orderid、bustype,syscode
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < fieldList.size(); i++) {
                String str = fieldList.get(i);
                if (str.equals("tag")) {
                    OrcMap orcMap = (OrcMap) pair.getFieldValue(i);
                    if (tags.size() > 0) {
                        for (String elem : tags.keySet()) {
                            try{
                                orcMap.put(new Text(elem), new Text(tags.get(elem)));
                            }catch (Exception e){
                                System.out.println(e.toString());
                                continue;
                            }
                        }
                    }
                    pair.setFieldValue(i, orcMap);
                    continue;
                }

                String strVal = tags.get(str);
                if (str.equals("request") && payload.indexOf("request")>0) {
                    strVal = model.getRequest().toString();
                } else if (str.equals("response") && payload.indexOf("response")>0) {
                    strVal = model.getResponse().toString();
                } else if (str.equals("message")) {
                    strVal = payload;
                }
                sb.append(str + " = " + strVal);
                if (strVal == null) {
                    strVal = "";
                }
                pair.setFieldValue(i, new Text(strVal));
            }
            outValue.value = pair;
            context.write(outKey, outValue);
        }
    }

    @Override
    public int run(String[] otherArgs) throws Exception {

        Configuration conf = getConf();
        if (otherArgs.length != 2) {
            System.out.println("argument is wrong,two argument is needed");
            return 0;
        }
        String schema = MRUtils.acquireRawSchema(schemaPath);
        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(conf, schema);
        conf.set("orc.mapred.output.schema", schema);
        conf.set("orc.mapred.map.output.value.schema", schema);

        System.out.println("param1:" + otherArgs[0]);
        System.out.println("param2:" + otherArgs[1]);
        FileSystem fs = FileSystem.get(conf);
        Job job = Job.getInstance(conf, otherArgs[0] + "_" + otherArgs[1] + " jiexi");
        job.setJarByClass(APPID100006883Resolve.class);
        job.setMapperClass(APPID100006883Resolve.APPID100006883Mapper.class);
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(OrcValue.class);
        job.getConfiguration().set("orc.mapred.value.type", "OrcValue");

        job.setReducerClass(OrcWriteReducer.class);
        job.setOutputFormatClass(OrcOutputFormat.class);

        job.setNumReduceTasks(300);
        List<String> partitionLists = FileUtils.generateWalletInput(otherArgs[0], otherArgs[1],otherArgs[1]);
        System.out.println("输入文件数量:" + partitionLists.size());
        MRUtils.addInputDirs(partitionLists, fs, job);
        String outputPath = FileUtils.getWalletOutputDir(otherArgs[0], otherArgs[1]);
        OrcOutputFormat.setOutputPath(job, new Path(outputPath));

        System.out.println("start to delete output:" + outputPath);

        boolean fileDelete = fs.delete(new Path(outputPath), true);
        if (fileDelete) {
            System.out.println("delete output:" + outputPath + " successful");
        } else {
            System.out.println("delete output:" + outputPath + " failed");
        }
        if (otherArgs[otherArgs.length - 1].equals("@")) {
            job.submit();
            return 0;
        } else {
            return job.waitForCompletion(true) ? 0 : 1;
        }
    }

    public static void main(String[] args) throws Exception {
        Configuration conf = new Configuration();
        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
        System.exit(ToolRunner.run(conf, new APPID100006883Resolve(), otherArgs));
    }
}
