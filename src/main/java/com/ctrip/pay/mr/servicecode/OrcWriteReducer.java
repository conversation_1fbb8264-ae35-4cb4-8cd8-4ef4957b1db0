package com.ctrip.pay.mr.servicecode;

import com.ctrip.pay.mr.utils.MRUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import org.apache.orc.OrcConf;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;

import java.io.IOException;

/**
 * @Author: ymfang
 * @Date: 2019/7/18 14:52
 */
public class OrcWriteReducer
        extends Reducer<Text, OrcValue, NullWritable, OrcStruct> {
    private int count = 0;
    private String schema = "";

    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        schema = context.getConfiguration().get("orc.mapred.output.schema");
        if(StringUtils.isEmpty(schema)){
            schema=MRUtils.acquireSchema("ORCDescription/servicecode.properties");
        }
    }

    @Override
    protected void reduce(Text key, Iterable<OrcValue> values, Context context) throws IOException, InterruptedException {
        if ((count++) < 3) {
            System.out.println("InnerReducer:" + count + ": uid=" + key);
        }
        OrcConf.MAPRED_SHUFFLE_VALUE_SCHEMA.setString(context.getConfiguration(), schema);
        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(context.getConfiguration(), schema);
        for (OrcValue itm : values) {
            context.write(NullWritable.get(), (OrcStruct) itm.value);
        }

    }
}