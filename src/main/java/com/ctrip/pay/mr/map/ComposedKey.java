package com.ctrip.pay.mr.map;

import org.apache.hadoop.io.WritableComparable;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;

/**
 * @Author: ymfang
 * @Date: 2019/8/19 14:52
 */

public class Composed<PERSON>ey implements WritableComparable<ComposedKey> {

    private String servicecode;
    private String uid;
    private String day = null;

    public ComposedKey(){}

    @Override
    public void write(DataOutput dataOutput) throws IOException {
        dataOutput.writeUTF(servicecode);
        dataOutput.writeUTF(uid);
        if (day != null) dataOutput.writeUTF(day);
    }

    @Override
    public void readFields(DataInput dataInput) throws IOException {
        try {
            servicecode = dataInput.readUTF();
            uid = dataInput.readUTF();
            day = dataInput.readUTF();
        } catch (Exception exp) {
            day = null;
        }

    }

    public String getServicecode() {
        return servicecode;
    }

    public void setServicecode(String servicecode) {
        this.servicecode = servicecode;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Override
    public int compareTo(ComposedKey o) {

        if (this.servicecode.compareTo(o.servicecode) == 0) {
            if(this.uid.compareTo(o.uid)!=0)
                return this.uid.compareTo(o.uid);
            return this.day.compareTo(o.day);
        }
        return this.servicecode.compareTo(o.servicecode);
    }


    @Override
    public String toString() {
        return "GroupKeyToHive{" +
                "servicecode='" + servicecode + '\'' +
                "uid='" + uid + '\'' +
                ", day='" + day + '\'' +
                '}';
    }


}
