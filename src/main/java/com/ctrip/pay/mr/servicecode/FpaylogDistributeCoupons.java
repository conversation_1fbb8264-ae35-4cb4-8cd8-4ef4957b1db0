//package com.ctrip.pay.mr.servicecode;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.ctrip.pay.mr.inputformat.CombinedOrcInputFormat;
//import com.ctrip.pay.mr.reducer.CbuWriteMultioutputReducer;
//import com.ctrip.pay.mr.servicecode.serialize.FpayLogRoutePreModel;
//import com.ctrip.pay.mr.servicecode.serialize.MessageParser;
//import com.ctrip.pay.mr.servicecode.serialize.TagParser;
//import com.ctrip.pay.mr.utils.*;
//import com.ctrip.payment.router.front.api.*;
//import org.apache.commons.lang.ArrayUtils;
//import org.apache.commons.lang.StringUtils;
//import org.apache.hadoop.conf.Configuration;
//import org.apache.hadoop.conf.Configured;
//import org.apache.hadoop.fs.FileStatus;
//import org.apache.hadoop.fs.FileSystem;
//import org.apache.hadoop.fs.Path;
//import org.apache.hadoop.io.NullWritable;
//import org.apache.hadoop.io.Text;
//import org.apache.hadoop.io.compress.CompressionCodec;
//import org.apache.hadoop.io.compress.SnappyCodec;
//import org.apache.hadoop.mapreduce.Job;
//import org.apache.hadoop.mapreduce.Mapper;
//import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
//import org.apache.hadoop.mapreduce.lib.output.LazyOutputFormat;
//import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
//import org.apache.hadoop.util.GenericOptionsParser;
//import org.apache.hadoop.util.Tool;
//import org.apache.hadoop.util.ToolRunner;
//import org.apache.orc.OrcConf;
//import org.apache.orc.TypeDescription;
//import org.apache.orc.mapred.OrcStruct;
//import org.apache.orc.mapred.OrcValue;
//import org.apache.orc.mapreduce.OrcOutputFormat;
//
//import java.io.IOException;
//import java.util.*;
//import java.util.stream.Collectors;
//
//public class FpaylogDistributeCoupons extends Configured implements Tool {
//    public static String schemaPath = "ORCDescription/ods_fpaylog_route_pre.properties";
//
//    public static class CbuLogMapper
//            extends Mapper<NullWritable, OrcStruct, Text, OrcValue> {
//        private Text outKey = new Text();
//        private OrcValue outValue = new OrcValue();
//        //对应orc表数据结构
//        private OrcStruct pair = null;
//        private String schema = "";
//        //对应hive表字段
//        private List<String> fieldList = new ArrayList<>();
//
//        private HashMap<String, String> discountKeyAndPromotionIdMap;
//
//        @Override
//        protected void setup(Context context) throws IOException, InterruptedException {
//            schema = context.getConfiguration().get("orc.mapred.output.schema");
//            String disCountKeyAndPromotionIdMappingStr = context.getConfiguration().get("disCountKeyAndPromotionIdMappingStr");
//            discountKeyAndPromotionIdMap = mapStringToMap(disCountKeyAndPromotionIdMappingStr);
//            System.out.println("from_setup_get_discountKeyAndPromotionIdMap : " + discountKeyAndPromotionIdMap.toString());
//            System.out.println("start to step up schema:" + schema);
//            fieldList = MRUtils.initalFields(schemaPath);
//            TypeDescription typeDescription = TypeDescription.fromString(schema);
//            typeDescription.createRowBatch(100);
//            pair = (OrcStruct) OrcStruct.createValue(typeDescription);
//        }
//
//        @Override
//        protected void map(NullWritable key, OrcStruct value, Context context) throws IOException, InterruptedException {
//
//            //从[[开始截取字符串并移除字符串中的特殊字符；
//            String msg = MessageParser.parseMsg(value.getFieldValue(0).toString());
//            //解析tag转换成map；
//            Map<String, String> tagsMap = TagParser.parseTags(msg);
//            // 获取message字符串；
//            String messageStr = MessageParser.parseBodyForPayDispath(msg);
//            if (tagsMap.keySet().size() == 0) {
//                return;
//            }
//
//
//            //根据条件获取支付下发主日志；
//            String uid = tagsMap.getOrDefault("uid", "");
//            String orderId = tagsMap.getOrDefault("orderid", "");
//            String serviceCode = tagsMap.getOrDefault("servicecode", "");
//            if (!serviceCode.matches("(.*)[0-9]+(.*)")) {
//                System.out.println("abandom:" + uid + "_" + serviceCode);
//                return;
//            }
//            String paymentAppId = tagsMap.getOrDefault("paymentappid", "");
//            String command = tagsMap.getOrDefault("command", "");
//            String serviceEntry = tagsMap.getOrDefault("serviceentry", "");
//            String logType = tagsMap.getOrDefault("logtype", "");
//
//            //只解析tag中包含orderid和uid的数据；
////            if (!serviceCode.equals("31104404") && (!(tagsMap.containsKey("orderid") || tagsMap.containsKey("uid")))) {
////                return;
////            }
//
//            //只解析tag中包含orderid和uid的数据；
//            if (!(tagsMap.containsKey("servicecode") || tagsMap.containsKey("uid"))) {
//                return;
//            }
////            boolean isNeedData = paymentAppId.equals("100033482") && serviceCode.equals("31100102")
////                    && command.equals("paymentListSearch") && serviceEntry.equals("true")
////                    && logType.equals("SERVICE");
//            boolean isNeedData = command.equals("paymentListSearch");
//            //boolean isNeedData = true;
//            //解析需要的数据；
//            if (isNeedData) {
//                FpayLogRoutePreModel fpayLogRoutePreModel = null;
//                try {
//                    fpayLogRoutePreModel = JSON.parseObject(messageStr, FpayLogRoutePreModel.class);
//                } catch (Exception e) {
//                    System.out.println("parse message to  FpayLogRoutePreModel exception : " + e.toString());
//
//                }
//                fpayLogRoutePreModel = Optional.ofNullable(fpayLogRoutePreModel).orElseGet(FpayLogRoutePreModel::new);
//                Date start = fpayLogRoutePreModel.getStart();
//                Long dateLong = DateUtil.getDateLong(start);
//                //不能用_作为分隔符，因为部分uid就是以_开头的,dateLong,打散key；
//                outKey.set(uid + MRUtils.col_seperator + serviceCode + MRUtils.col_seperator + dateLong);
//                outValue.value = givePairValue(msg, tagsMap, messageStr, fieldList, fpayLogRoutePreModel, uid, orderId, command, discountKeyAndPromotionIdMap, pair);
//            }
//            //解析其他数据；
//            if (!isNeedData) {
//                Long dateLong = DateUtil.getDateLong(new Date());
//                outKey.set(uid + MRUtils.col_seperator + serviceCode + MRUtils.col_seperator + dateLong);
//                outValue.value = givePairValue(msg, tagsMap, messageStr, fieldList, uid, orderId, command, pair);
//            }
//            context.write(outKey, outValue);
//        }
//
//        //解析需要使用的数据；
//        public static OrcStruct givePairValue(String oriStr, Map<String, String> tagsMap, String messageStr, List<String> fieldList, FpayLogRoutePreModel fpayLogRoutePreModel,
//                                              String uid, String orderId, String command,
//                                              HashMap<String, String> discountKeyAndPromotionIdMap, OrcStruct pair) {
//
//            PaymentListSearchRequest paymentListSearchRequest = Optional.ofNullable(fpayLogRoutePreModel.getRequest())
//                    .map(JsonUtility::toJson)
//                    .map(x -> JSON.parseObject(x, PaymentListSearchRequest.class))
//                    .orElseGet(PaymentListSearchRequest::new);
//            System.out.println("paymentListSearchRequest : " + paymentListSearchRequest.toString());
//
//            PaymentListSearchResponse paymentListSearchResponse = Optional.ofNullable(fpayLogRoutePreModel.getResponse())
//                    .map(JsonUtility::toJson)
//                    .map(x -> {
//                        try {
//                            JSONObject.parseObject(x);
//                            return x;
//                        } catch (Exception e) {
//                            return null;
//                        }
//                    })
//                    .map(x -> JSON.parseObject(x, PaymentListSearchResponse.class))
//                    .orElseGet(PaymentListSearchResponse::new);
//            System.out.println("paymentListSearchResponse : " + paymentListSearchResponse);
//
//            Optional<JSONObject> payOrderInfObjOpt = Optional.ofNullable(paymentListSearchResponse.getOrderInfo())
//                    .map(OrderInfo::getPayOrderInfo)
//                    .map(JSONObject::parseObject);
//
//            //常用支付方式集合；
//            Optional<List<DisplayPayway>> displayPayWays = Optional.of(paymentListSearchResponse)
//                    .map(PaymentListSearchResponse::getDisplayInfo)
//                    .map(DisplayInfo::getOwnPayDisplayInfo)
//                    .map(OwnPayDisplayInfo::getDisplayPayways);
//
//            Optional<List<DisplayPayway>> bankCardList = displayPayWays
//                    .map(x -> x.stream().filter(y -> Optional.ofNullable(y.getCategory()).orElse("").equals("BankCard") && !y.isIsHide() && !StringUtils.isEmpty(y.getCardInfoId())).collect(Collectors.toList()));
//
//
//            Optional<DisplayPayway> displayPayWay1 = bankCardList.map(x -> {
//                if (x.size() >= 1) {
//                    return x.get(0);
//                }
//                return new DisplayPayway();
//            });
//
//            Optional<DisplayPayway> displayPayWay2 = bankCardList.map(x -> {
//                if (x.size() >= 2) {
//                    return x.get(1);
//                }
//                return new DisplayPayway();
//            });
//
//            fieldList.forEach(field -> {
//                int index = fieldList.indexOf(field);
//                String value = "";
//                switch (field) {
//                    case "uid":
//                        value = uid;
//                        break;
//                    case "order_id":
//                        value = orderId;
//                        break;
//                    case "plat":
//                        value = Optional.ofNullable(paymentListSearchRequest.getRequestHead()).map(RequestHead::getPlatform).orElse("");
//                        break;
//                    case "syscode":
//                        value = Optional.ofNullable(paymentListSearchRequest.getRequestHead()).map(RequestHead::getSysCode).orElse("");
//                        break;
//                    case "merchant_id":
//                        value = tagsMap.getOrDefault("merchantid", "");
//                        break;
//                    case "service_version":
//                        value = tagsMap.getOrDefault("version", "");
//                        break;
//
//                    case "amount":
//                        value = Optional.ofNullable(paymentListSearchResponse.getOrderInfo()).map(OrderInfo::getOrderAmount).orElse("");
//                        break;
//
//                    case "token_id":
//                        value = Optional.ofNullable(paymentListSearchRequest.getPayToken()).orElse("");
//                        break;
//
//                    case "pdiscount_info":
//                        value = Optional.ofNullable(paymentListSearchResponse.getPayDiscountInfoList())
//                                .map(x -> x.stream().map(JsonUtils::toJson).filter(StringUtils::isNotEmpty).collect(Collectors.toList()))
//                                .map(ArrayUtils::toString)
//                                .orElse("");
//                        break;
//
//                    case "promotionIdArr":
//                        value = Optional.ofNullable(paymentListSearchResponse.getPayDiscountInfoList())
//                                .map(x -> x.stream().filter(y -> null == y.getDiscountStatus() || !y.getDiscountStatus().contains("14")).map(PayDiscountInfo::getPromotionId).filter(StringUtils::isNotEmpty).collect(Collectors.toList()))
//                                .map(ArrayUtils::toString)
//                                .orElse("");
//                        break;
//                    case "card_cnt":
//                        value = Optional.ofNullable(paymentListSearchResponse.getPayCatalogInfo())
//                                .map(PayCatalogInfo::getBankCardList)
//                                .map(x -> String.valueOf(x.size()))
//                                .orElse("0");
//                        break;
//
//                    case "default_paymentway_id":
//                        value = displayPayWays
//                                .map(x -> x.stream().filter(y -> Optional.ofNullable(y).isPresent() && Optional.ofNullable(y.isSelected()).isPresent() && Optional.ofNullable(y.isSelected()).get()).map(z -> Optional.ofNullable(z.getBrandId()).isPresent() ? z.getBrandId() : Optional.ofNullable(z.getCategory()).orElse("")).filter(StringUtils::isNotEmpty).collect(Collectors.toList()))
//                                .map(x -> StringUtils.strip(x.toString(), "[]"))
//                                .orElse("");
//
//                        if (StringUtils.isEmpty(value)) {
//                            Optional<List<ThirdPartyDisplayInfo>> thirdPartyDisplayInfos = Optional.of(paymentListSearchResponse)
//                                    .map(PaymentListSearchResponse::getDisplayInfo)
//                                    .map(DisplayInfo::getThirdPartyDisplayInfoList)
//                                    .map(x -> x.stream().filter(ThirdPartyDisplayInfo::isSelected).collect(Collectors.toList()));
//
//                            value = thirdPartyDisplayInfos
//                                    .map(x -> {
//                                        if (x.size() > 0) {
//                                            return x.get(0).getBrandId();
//                                        } else {
//                                            return "";
//                                        }
//                                    }).orElse("");
//                        }
//                        break;
//
//                    case "request_id":
//                        value = payOrderInfObjOpt.map(x -> x.getJSONObject("header")).map(x -> x.getString("requestId"))
//                                .orElse("");
//                        break;
//
//                    case "user_status":
//                        value = Optional.of(paymentListSearchResponse)
//                                .map(PaymentListSearchResponse::getPayCatalogInfo)
//                                .map(PayCatalogInfo::getBankCardList)
//                                .map(x -> x.stream().filter(y -> Optional.ofNullable(y.getBrandId()).orElse("").equals("CC_CMB")).map(BankCardInfo::getStatus).filter(Objects::nonNull).flatMap(Collection::stream).filter(StringUtils::isNotEmpty).collect(Collectors.toList()))
//                                .map(ArrayUtils::toString)
//                                .orElse("");
//                        break;
//
//                    case "promotionid_disp":
//                        value = Optional.of(paymentListSearchResponse)
//                                .map(PaymentListSearchResponse::getDisplayInfo)
//                                .map(DisplayInfo::getMarketingDisplayInfoList)
//                                .map(x -> x.stream().filter(y -> Optional.ofNullable(y).isPresent() && Optional.ofNullable(y.isIsHide()).isPresent() && !Optional.ofNullable(y.isIsHide()).get()).map(MarketingDisplayInfo::getDiscountKey).filter(Objects::nonNull).distinct().map(discountKeyAndPromotionIdMap::get).filter(StringUtils::isNotEmpty).collect(Collectors.toList()))
//                                .map(ArrayUtils::toString)
//                                .orElse("");
//                        break;
//
//                    case "promotionid_newcard":
//                        value = displayPayWays
//                                .map(x -> x.stream().filter(y -> Optional.ofNullable(y.getCategory()).orElse("").equals("BankCard")
//                                        && !Optional.ofNullable(y.getCardInfoId()).isPresent()))
//                                .map(x -> x.map(DisplayPayway::getPayDiscountKeys).filter(Objects::nonNull).flatMap(Collection::stream))
//                                .map(x -> x.map(discountKeyAndPromotionIdMap::get).filter(StringUtils::isNotEmpty).distinct())
//                                .map(x -> x.collect(Collectors.toList()))
//                                .map(ArrayUtils::toString)
//                                .orElse("");
//                        break;
//
//                    case "promotionid_usedcard1":
//                        value = displayPayWay1.map(DisplayPayway::getPayDiscountKeys)
//                                .map(x -> x.stream().map(discountKeyAndPromotionIdMap::get).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList()))
//                                .map(ArrayUtils::toString)
//                                .orElse("");
//
//                        break;
//
//                    case "promotionid_usedcard2":
//                        value = displayPayWay2.map(DisplayPayway::getPayDiscountKeys)
//                                .map(x -> x.stream().map(discountKeyAndPromotionIdMap::get).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList()))
//                                .map(ArrayUtils::toString)
//                                .orElse("");
//
//                        break;
//
//                    case "promotionid_usedcard1_max":
//
//                        value = displayPayWay1
//                                .map(DisplayPayway::getDiscountStatusInfoList)
//                                .map(x -> x.stream().max(Comparator.comparing(DiscountStatusInfo::getAmount, Comparator.nullsFirst(Comparator.naturalOrder()))).map(DiscountStatusInfo::getKey).map(discountKeyAndPromotionIdMap::get))
//                                .flatMap(x -> x)
//                                .orElse("");
//                        break;
//
//                    case "promotionid_usedcard2_max":
//
//                        value = displayPayWay2
//                                .map(DisplayPayway::getDiscountStatusInfoList)
//                                .map(x -> x.stream().max(Comparator.comparing(DiscountStatusInfo::getAmount, Comparator.nullsFirst(Comparator.naturalOrder()))).map(DiscountStatusInfo::getKey).map(discountKeyAndPromotionIdMap::get))
//                                .flatMap(x -> x)
//                                .orElse("");
//
//                        break;
//
//                    case "client_supported":
//                        value = Optional.of(paymentListSearchRequest)
//                                .map(PaymentListSearchRequest::getMarkInfo)
//                                .map(MarkInfo::getSupportPayInfos)
//                                .map(Object::toString)
//                                .orElse("");
//
//                        break;
//
//                    case "user_from":
//                        value = Optional.of(paymentListSearchRequest)
//                                .map(PaymentListSearchRequest::getMarkInfo)
//                                .map(MarkInfo::getUserFrom)
//                                .map(ArrayUtils::toString)
//                                .orElse("");
//                        break;
//
//                    case "disable_discount":
//                        value = payOrderInfObjOpt.map(x -> x.getJSONObject("payExtend"))
//                                .map(x -> x.getString("disableDiscount"))
//                                .orElse("");
//                        break;
//
//                    case "h5plat":
//                        Optional<String> sysCodeOpt = Optional
//                                .ofNullable(paymentListSearchRequest.getRequestHead())
//                                .map(RequestHead::getSysCode)
//                                .filter(x -> x.equals("09"));
//                        if (sysCodeOpt.isPresent()) {
//                            value = Optional.of(paymentListSearchRequest)
//                                    .map(PaymentListSearchRequest::getMarkInfo)
//                                    .map(MarkInfo::getUserFrom)
//                                    .map(x -> x.stream().reduce((first, second) -> second).orElse(""))
//                                    .orElse("");
//                        }
//
//                        break;
//
//                    case "ruse_type":
//                        value = Optional.of(paymentListSearchResponse)
//                                .map(PaymentListSearchResponse::getOrderInfo)
//                                .map(OrderInfo::getPayType)
//                                .map(String::valueOf)
//                                .orElse("");
//                        break;
//
//                    case "source_id":
//                        value = Optional.of(paymentListSearchRequest)
//                                .map(PaymentListSearchRequest::getRequestHead)
//                                .map(RequestHead::getDeviceInfo)
//                                .map(DeviceInfo::getSourceId)
//                                .orElse("");
//                        break;
//                    case "order_currency":
//                        value = Optional.of(paymentListSearchResponse)
//                                .map(PaymentListSearchResponse::getOrderInfo)
//                                .map(OrderInfo::getOrderCurrency)
//                                .map(String::valueOf)
//                                .orElse("");
//                        break;
//                    case "appid":
//                        value = Optional.ofNullable(fpayLogRoutePreModel.getAppId()).orElse("");
//                        break;
//                    case "flag":
//                        value = "";
//                        break;
//                    case "extmap":
//                        HashMap<String, String> map = new HashMap<>();
//                        Optional<KeyValueItem> keyValueItem = Optional.of(paymentListSearchResponse)
//                                .map(PaymentListSearchResponse::getDisplayInfo)
//                                .map(DisplayInfo::getThirdPayBottomDiscount);
//                        Optional<String> keyOpt = keyValueItem
//                                .map(KeyValueItem::getKey);
//                        Optional<String> valueOpt = keyValueItem
//                                .map(KeyValueItem::getValue)
//                                .map(discountKeyAndPromotionIdMap::get);
//
//                        if (keyOpt.isPresent() && valueOpt.isPresent()) {
//                            map.put("second_retention_promotionid", valueOpt.get());
//
//                        }
//
//                        Optional<List<PayDiscountInfo>> payDiscountInfos = Optional.of(paymentListSearchResponse)
//                                .map(PaymentListSearchResponse::getPayDiscountInfoList);
//
//                        Optional<List<PayDiscountInfo>> payDiscountInfosFilter = payDiscountInfos
//                                .map(x -> x.stream().filter(y -> Optional.ofNullable(y.getDiscountStatus()).isPresent() && Optional.ofNullable(y.getDiscountStatus()).get().contains("10")).collect(Collectors.toList()));
//
//                        if (payDiscountInfosFilter.isPresent() && payDiscountInfosFilter.get().size() > 0) {
//                            PayDiscountInfo payDiscountInfo = payDiscountInfosFilter.get().get(0);
//                            String promotionIdPop = Optional.ofNullable(payDiscountInfo.getPromotionId())
//                                    .orElse("");
//                            map.put("promotionId_pop", promotionIdPop);
//
//                        }
//
//                        //获取second_retention_promotionid的值；
//                        String second_retention_promotionid = Optional.of(paymentListSearchResponse)
//                                .map(PaymentListSearchResponse::getDisplayInfo)
//                                .map(DisplayInfo::getThirdPayBottomDiscount)
//                                .map(KeyValueItem::getValue)
//                                .map(discountKeyAndPromotionIdMap::get)
//                                .orElse("");
//                        if (!second_retention_promotionid.isEmpty()) {
//                            map.put("second_retention_promotionid", second_retention_promotionid);
//                        }
//
//                        value = map.toString();
//                        break;
//                    case "command":
//                        value = command;
//                        break;
//                    case "request_time":
//                        value = Optional.ofNullable(fpayLogRoutePreModel.getStart()).map(DateUtil::format).orElse("");
//                        break;
//                    case "message_raw":
//                        value = oriStr;
//                        break;
//                    case "tag":
//                        value = Optional.ofNullable(tagsMap).map(JsonUtils::toJson).map(x -> x.replaceAll(" ", "")).orElse("");
//                        break;
//                    case "message":
//                        value = messageStr;
//                        break;
//                    case "request":
//                        value = Optional.ofNullable(fpayLogRoutePreModel.getRequest()).map(Object::toString).orElse("");
//                        break;
//
//                    case "response":
//                        value = Optional.ofNullable(fpayLogRoutePreModel.getResponse()).map(Object::toString).orElse("");
//                        break;
//                    case "paytype":
//                        String payType = Optional.of(paymentListSearchResponse)
//                                .map(PaymentListSearchResponse::getOrderInfo)
//                                .map(OrderInfo::getPayType)
//                                .map(Object::toString)
//                                .orElse("");
//
//                        String payScene = Optional.of(paymentListSearchRequest)
//                                .map(PaymentListSearchRequest::getRequestHead)
//                                .map(RequestHead::getPayScence)
//                                .orElse("");
//                        value = StringUtils.isNotEmpty(payType) ? payType : payScene;
//                        break;
//                    //cardInfoId一样，payCatalogInfo.bankCardList.status集合里不包含3
//                    case "brandid_usedcard1":
//
//                        value = displayPayWays
//                                .map(x -> x.stream().filter(y -> Optional.ofNullable(y).isPresent() && Optional.ofNullable(y.getCategory()).isPresent() && y.getCategory().equals("BankCard") && Optional.ofNullable(y.getCardInfoId()).isPresent()).collect(Collectors.toList()))
//                                .map(x -> {
//                                    String result = "";
//                                    if (x.size() >= 1) {
//                                        DisplayPayway displayPayway = x.get(0);
//                                        String brandId = displayPayway.getBrandId();
//                                        String cardInfoId = displayPayway.getCardInfoId();
//
//                                        if (StringUtils.isNotEmpty(brandId)) {
//                                            result = brandId;
//                                        }
//
//                                        Optional<List<BankCardInfo>> bankCardInfos = Optional.of(paymentListSearchResponse)
//                                                .map(PaymentListSearchResponse::getPayCatalogInfo)
//                                                .map(PayCatalogInfo::getBankCardList)
//                                                .map(y -> y.stream().filter(z -> Optional.ofNullable(z.getCardInfoId()).isPresent() && z.getCardInfoId().equals(cardInfoId)).collect(Collectors.toList()));
//
//                                        if (bankCardInfos.isPresent()) {
//                                            List<BankCardInfo> bankCardInfos1 = bankCardInfos.get();
//                                            for (int i = 0; i < bankCardInfos1.size(); i++) {
//                                                BankCardInfo bankCardInfo = bankCardInfos1.get(i);
//                                                if (bankCardInfo.getStatus().contains("3")) {
//                                                    result = "";
//                                                }
//                                            }
//                                        }
//                                    }
//                                    return result;
//                                })
//                                .orElse("");
//                        break;
//
//                    //cardInfoId一样，payCatalogInfo.bankCardList.status集合里不包含3
//                    case "brandid_usedcard2":
//                        value = displayPayWays
//                                .map(x -> x.stream().filter(y -> Optional.ofNullable(y).isPresent() && Optional.ofNullable(y.getCategory()).isPresent() && y.getCategory().equals("BankCard") && Optional.ofNullable(y.getCardInfoId()).isPresent()).collect(Collectors.toList()))
//                                .map(x -> {
//                                    String result = "";
//                                    if (x.size() >= 2) {
//                                        DisplayPayway displayPayway = x.get(1);
//                                        String brandId = displayPayway.getBrandId();
//                                        if (StringUtils.isNotEmpty(brandId)) {
//                                            result = brandId;
//                                        }
//                                        String cardInfoId = displayPayway.getCardInfoId();
//
//                                        Optional<List<BankCardInfo>> bankCardInfos = Optional.of(paymentListSearchResponse)
//                                                .map(PaymentListSearchResponse::getPayCatalogInfo)
//                                                .map(PayCatalogInfo::getBankCardList)
//                                                .map(y -> y.stream().filter(z -> Optional.ofNullable(z.getCardInfoId()).isPresent() && z.getCardInfoId().equals(cardInfoId)).collect(Collectors.toList()));
//
//                                        if (bankCardInfos.isPresent()) {
//                                            List<BankCardInfo> bankCardInfos1 = bankCardInfos.get();
//                                            for (int i = 0; i < bankCardInfos1.size(); i++) {
//                                                BankCardInfo bankCardInfo = bankCardInfos1.get(i);
//                                                if (bankCardInfo.getStatus().contains("3")) {
//                                                    result = "";
//                                                }
//                                            }
//                                        }
//                                    }
//                                    return result;
//                                })
//                                .orElse("");
//                        break;
//
//                }
//                pair.setFieldValue(index, new Text(value));
//            });
//            return pair;
//        }
//
//
//        //解析暂时不使用的数据；
//        public static OrcStruct givePairValue(String oriStr, Map<String, String> tagsMap, String messageStr, List<String> fieldList,
//                                              String uid, String orderId, String command, OrcStruct pair) {
//            fieldList.forEach(field -> {
//                int index = fieldList.indexOf(field);
//                String value = "";
//                switch (field) {
//                    case "uid":
//                        value = uid;
//                        break;
//                    case "order_id":
//                        value = orderId;
//                        break;
//                    case "appid":
//                        value = tagsMap.getOrDefault("paymentappid", "");
//                        break;
//                    case "command":
//                        value = command;
//                        break;
//                    case "tag":
//                        value = Optional.ofNullable(tagsMap).map(JsonUtils::toJson).orElse("");
//                        break;
//                    case "message":
//                        value = messageStr;
//                        break;
//                    case "message_raw":
//                        value = oriStr;
//                        break;
//
//                    case "plat":
//                        value = tagsMap.getOrDefault("plat", "");
//                        break;
//
//                    case "syscode":
//                        value = tagsMap.getOrDefault("syscode", "");
//                        break;
//
//                    case "merchantid":
//                        value = tagsMap.getOrDefault("merchantid", "");
//                        break;
//
//                    case "service_version":
//                        value = tagsMap.getOrDefault("version", "");
//                        break;
//
//                    case "tokenid":
//                        value = tagsMap.getOrDefault("tokenid", "");
//                        break;
//
//                    case "request_time":
//                        value = tagsMap.getOrDefault("logtime", "");
//                        break;
//
//                }
//                pair.setFieldValue(index, new Text(value));
//            });
//            return pair;
//        }
//
//    }
//
//
//    @Override
//    public int run(String[] otherArgs) throws Exception {
//        Configuration conf = getConf();
//        if (otherArgs.length != 1) {
//            System.out.println("argument is wrong,single argument is required");
//            return 0;
//        }
//        String schema = MRUtils.acquireRawSchema(schemaPath);
//        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(conf, schema);
//        OrcConf.MAPRED_SHUFFLE_VALUE_SCHEMA.setString(conf, schema);
//
//        conf.set("mapreduce.input.fileinputformat.split.maxsize", "134217728");
//        conf.set("mapreduce.task.io.sort.mb", "200");
//        conf.setBoolean("mapreduce.map.output.compress", true);
//        conf.setClass("mapreduce.map.output.compress.code", SnappyCodec.class, CompressionCodec.class);
//        //map内存调大
//        conf.set("mapreduce.map.memory.mb", "8096");
//        conf.set("mapreduce.map.java.opts", "-Xmx6000m");
//        //reduce内存调大
//        conf.set("mapreduce.reduce.memory.mb", "8096");
//        conf.set("mapreduce.reduce.java.opts", "-Xmx6000m");
//        conf.set("mapreduce.reduce.shuffle.memory.limit.percent", "0.15");
////        conf.set("mapreduce.reduce.shuffle.merge.percent", "0.2");
//        conf.set("mapreduce.reduce.speculative", "false");
//
//        conf.setBoolean(OrcOutputFormat.SKIP_TEMP_DIRECTORY, true);
//        conf.set("mapreduce.map.output.value.class", "org.apache.orc.mapred.OrcValue");
//
//        System.out.println("param1:" + otherArgs[0]);
//        FileSystem fs = FileSystem.get(conf);
//        Job job = Job.getInstance(conf, "ods_fpaylog_distribute_coupons etl");
//        job.setReduceSpeculativeExecution(false);
//        job.setJarByClass(FpaylogDistributeCoupons.class);
//        job.setMapperClass(CbuLogMapper.class);
//        job.setMapOutputKeyClass(Text.class);
//        job.setMapOutputValueClass(OrcValue.class);
//        job.setInputFormatClass(CombinedOrcInputFormat.class);
//        job.setReducerClass(CbuWriteMultioutputReducer.class);
//        job.setOutputKeyClass(NullWritable.class);
//        job.setOutputValueClass(OrcStruct.class);
//
//        job.setNumReduceTasks(1000);
//        String root = "/user/bifin/pay/pay_log_raw_orc/destination/fnc_log_created";
//
//
////        List<String> partitionLists = FileUtils.generateRdInputPrefix(root, otherArgs[0], otherArgs[0]);
//
//
//        FileStatus[] status = fs.listStatus(new Path(root));
//        //获取topic信息
//        List<String> partitionLists = new ArrayList<>();
//
//        for (FileStatus s : status) {
//            String filePath = s.getPath().toString();
//            // 统计topic列表
//            FileStatus[] dest = fs.listStatus(new Path(filePath));
//
//            for (FileStatus dst : dest) {
//                String subFilePath = dst.getPath().toString();
//                subFilePath = subFilePath.substring(0, subFilePath.indexOf("/hourly"));
//                //必须要包含appid
//                if (subFilePath.contains("fnc_log_created") && subFilePath.matches("(.*)[0-9]+(.*)") && (subFilePath.contains("100043256") || subFilePath.contains("100040255"))) {
//                    partitionLists.addAll(FileUtils.generateRdInputPrefix(subFilePath, otherArgs[0], otherArgs[0]));
//                    System.out.println("输入文件路径:" + subFilePath);
//                }
//            }
//        }
//
//
//        Collections.shuffle(partitionLists);
//        MRUtils.addInputDirs(partitionLists, fs, job);
//
//        System.out.println("输入文件个数" + partitionLists.size());
//        if (partitionLists.size() > 0) {
//            System.out.println("输入文件格式" + partitionLists.get(0));
//        } else {
//            System.out.println("输入文件数为0");
//            return -1;
//        }
//        String outputPath = String.format("/user/biuser/warehouse/etl/Ods_PayDB.db/ods_fpaylog_distribute_coupons" + "/dt=%s/", otherArgs[0]);
//        job.getConfiguration().set("cbu.path", outputPath);
//
//        FileOutputFormat.setOutputPath(job, new Path(outputPath));
//        MultipleOutputs.addNamedOutput(job, "cbulog", OrcOutputFormat.class, NullWritable.class, OrcStruct.class);
//        LazyOutputFormat.setOutputFormatClass(job, OrcOutputFormat.class);
//        System.out.println("start to delete output:" + outputPath);
//
//        boolean fileDelete = fs.delete(new Path(outputPath), true);
//        if (fileDelete) {
//            System.out.println("delete output:" + outputPath + " successful");
//        } else {
//            System.out.println("delete output:" + outputPath + " failed");
//        }
//        if (otherArgs[otherArgs.length - 1].equals("@")) {
//            job.submit();
//            return 0;
//        } else {
//            return job.waitForCompletion(true) ? 0 : 1;
//        }
//    }
//
//
//    public static void main(String[] args) throws Exception {
//        Configuration conf = new Configuration();
//        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
//        String disCountKeyAndPromotionIdMappingStr = getDisCountKeyAndPromotionIdMappingStr(otherArgs);
//        conf.set("disCountKeyAndPromotionIdMappingStr", disCountKeyAndPromotionIdMappingStr);
//        String[] newOtherArgs = {otherArgs[0]};
//        System.exit(ToolRunner.run(conf, new FpaylogDistributeCoupons(), newOtherArgs));
//    }
//
//    public static String getDisCountKeyAndPromotionIdMappingStr(String[] strings) throws IOException {
//        HashMap<String, String> hashMap = new HashMap<>();
//        for (int i = 3; i < strings.length; i = i + 2) {
//            hashMap.put(strings[i], strings[i + 1]);
//        }
//
//        return hashMap.toString();
//    }
//
//    public static HashMap<String, String> mapStringToMap(String str) {
//        str = str.substring(1, str.length() - 1);
//        String[] strs = str.split(",");
//        HashMap<String, String> map = new HashMap();
//        for (String string : strs) {
//            String key = string.split("=")[0].trim();
//            String value = string.split("=")[1];
//            map.put(key, value);
//        }
//        return map;
//    }
//
//}
