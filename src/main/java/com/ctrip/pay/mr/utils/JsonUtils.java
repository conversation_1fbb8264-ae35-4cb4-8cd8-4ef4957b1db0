package com.ctrip.pay.mr.utils;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.Version;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;

public class JsonUtils {

    private static String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        OBJECT_MAPPER.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        OBJECT_MAPPER.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.NONE);
        OBJECT_MAPPER.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);

        SimpleModule localDateModule = new SimpleModule("LocalDateTimeModule", Version.unknownVersion());

        OBJECT_MAPPER.registerModule(localDateModule);
    }

    public static ObjectMapper mapperInstance() {
        return OBJECT_MAPPER;
    }

    public static String toJson(Object o) {
        if (o == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writeValueAsString(o);
        } catch (Exception e) {
            System.out.println("to json error." + e.toString());

        }
        return null;
    }

    public static <T> T parseJson(String json, Class<T> clazz) {
        if (json == null || json.length() == 0) {
            return (T) null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (Exception e) {
            System.out.println("parse json error." + e.toString());
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseJson(String json, TypeReference<T> typeReference) {
        if (json == null || json.length() == 0) {
            return (T) null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (Exception e) {
            System.out.println("parse json error." + e.toString());
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseJson(String json, JavaType type) {
        if (json == null || json.length() == 0) {
            return (T) null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, type);
        } catch (Exception e) {
            System.out.println("parse json error." + e.toString());
            throw new RuntimeException(e);
        }
    }

    public static JsonNode parseJsonNode(String json) {
        if (json == null || json.length() == 0) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readTree(json);
        } catch (Exception e) {
            System.out.println("parse json node error." + e.toString());
            throw new RuntimeException(e);
        }
    }

    public static Boolean isJson(String str){
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            objectMapper.readTree(str);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }
}
