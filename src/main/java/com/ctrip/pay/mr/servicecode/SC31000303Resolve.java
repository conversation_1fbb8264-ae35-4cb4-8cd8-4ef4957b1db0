package com.ctrip.pay.mr.servicecode;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.pay.mr.servicecode.serialize.MessageParser;
import com.ctrip.pay.mr.servicecode.serialize.TagParser;
import com.ctrip.pay.mr.servicecode.serialize.TraceModel;
import com.ctrip.pay.mr.utils.*;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.util.GenericOptionsParser;
import org.apache.hadoop.util.Tool;
import org.apache.hadoop.util.ToolRunner;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;
import org.apache.orc.mapreduce.OrcOutputFormat;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SC31000303Resolve extends Configured implements Tool {
    public static String schemaPath = "ORCDescription/servicecode.properties";
    public static class SC31000303Mapper
            extends Mapper<LongWritable, Text, Text, OrcValue> {
        int count = 0;
        private Text outKey = new Text();
        private OrcValue outValue = new OrcValue();
        //对应hive表字段
        private List<String> fieldList = new ArrayList<>();

        //对应orc表数据结构
        private OrcStruct pair = null;
        private String schema = "";

        @Override
        protected void setup(Context context) throws IOException, InterruptedException {
            fieldList = MRUtils.initalFields(schemaPath);
            schema = MRUtils.acquireRawSchema(schemaPath);
            pair = (OrcStruct) OrcStruct.createValue(TypeDescription.fromString(schema));
        }

        @Override
        protected void map(LongWritable key, Text value, Context context) throws IOException, InterruptedException {
            String msg = value.toString().trim();
            if (msg.length() < 20) { //异常行
                return;
            }
            //解析tag
            Map<String, String> tagsMap = TagParser.parseTags(msg);
            //找不到orderid或者uid的
            if (!(tagsMap.containsKey("orderid") || tagsMap.containsKey("uid"))) {
                return;
            }
            if (!(tagsMap.get("logtype").equalsIgnoreCase("paymentinfo")
                    ||tagsMap.get("logtype").equalsIgnoreCase("paymentinfosoa")
                    ||tagsMap.get("logtype").equalsIgnoreCase("paymentnotify"))) {
                return;
            }

            //解析msg
            String bodyStr = MessageParser.parseBody(msg);
            TraceModel model = null;

            try {
                model = JSON.parseObject(bodyStr, TraceModel.class);
            } catch (Exception e) {

            }
            if (bodyStr.startsWith("卡号段限制") || model == null){
                return;
            }
            try{
                JSONObject request = JSON.parseObject(model.getRequest().toString());
                model.setRequest(request);

                JSONObject respnse = JSON.parseObject(model.getResponse().toString());
                model.setResponse(respnse);

                JSONObject requestHead = JSON.parseObject(model.getRequestHead().toString());
                model.setRequestHead(requestHead);
            }catch (Exception ex){

            }

            if (model == null) {
                model = new TraceModel();
            }

            //计算出plat和resultcode
            if (!tagsMap.containsKey("plat")) {
                tagsMap.put("plat", model.getPlat() + "");
            }

            tagsMap.put("resultcode", model.getResultcode() + "");

            String orderid = "";
            String bustype ="";
            try {
                orderid = model.getRequestElement("orderid");
                bustype = model.getRequestElement("bustype");
            } catch (Exception e) {

            }

            if (!tagsMap.containsKey("orderid") && orderid != null) {
                tagsMap.put("orderid", orderid);
            }

            if (!tagsMap.containsKey("bustype") && bustype != null) {
                tagsMap.put("bustype", bustype);
            }

            //往Tags中添加OrderAmount
            if (!tagsMap.containsKey("OrderAmount") ) {
                try {
                    JSONObject request = JSON.parseObject(model.getRequest().toString());
                    if (request != null) {
                        JSONObject oinfo = request.getJSONObject("oinfo");
                        if (oinfo != null) {
                            tagsMap.put("OrderAmount", oinfo.get("oamount").toString());
                        } else {
                            oinfo = request.getJSONObject("OrderInfo");
                            if (oinfo != null) {
                                tagsMap.put("OrderAmount", oinfo.get("OrderAmount").toString());
                            }
                        }
                    }
                }catch (Exception e){

                }
            }

            String uid = "";
            if (tagsMap.containsKey("uid")) {
                uid = tagsMap.get("uid");
            }
            outKey.set(uid);
            OrcConf.MAPRED_SHUFFLE_VALUE_SCHEMA.setString(context.getConfiguration(), schema);
            OrcConf.MAPRED_OUTPUT_SCHEMA.setString(context.getConfiguration(), schema);

            pair = OutValueUtil.fillOutValue(tagsMap, fieldList, model, bodyStr, msg);
            outValue.value = pair;

            context.write(outKey, outValue);
        }
    }

    @Override
    public int run(String[] otherArgs) throws Exception {
        Configuration conf = getConf();
        if (otherArgs.length != 2) {
            System.out.println("argument is wrong, two arguments is needed.");
            return 0;
        }
        String schema = MRUtils.acquireRawSchema(schemaPath);
        OrcConf.MAPRED_OUTPUT_SCHEMA.setString(conf, schema);
        conf.set("orc.mapred.output.schema", schema);
        conf.set("orc.mapred.map.output.value.schema", schema);

        System.out.println("param1:" + otherArgs[0]);
        System.out.println("param2:" + otherArgs[1]);
        FileSystem fs = FileSystem.get(conf);
        Job job = Job.getInstance(conf, otherArgs[0] + "_" + otherArgs[1] + " jiexi");
        job.setJarByClass(SC31000303Resolve.class);
        job.setMapperClass(SC31000303Resolve.SC31000303Mapper.class);
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(OrcValue.class);
        job.getConfiguration().set("orc.mapred.value.type", "OrcValue");

        job.setReducerClass(OrcWriteReducer.class);
        job.setOutputFormatClass(OrcOutputFormat.class);

        job.setNumReduceTasks(300);
        List<String> partitionLists = FileUtils.generateServicecodeInput(otherArgs[0], otherArgs[1]);
        System.out.println("输入文件数量:" + partitionLists.size());
        MRUtils.addInputDirs(partitionLists, fs, job);
        String outputPath = FileUtils.getOutputDir(otherArgs[0], otherArgs[1]);
        OrcOutputFormat.setOutputPath(job, new Path(outputPath));

        System.out.println("start to delete output:" + outputPath);

        boolean fileDelete = fs.delete(new Path(outputPath), true);

        System.out.println("delete output:" + outputPath + (fileDelete ? "successful." : "failed."));

        if (otherArgs[otherArgs.length - 1].equals("@")) {
            job.submit();
            return 0;
        } else {
            return job.waitForCompletion(true) ? 0 : 1;
        }
    }

    public static void main(String[] args) throws Exception {
        Configuration conf = new Configuration();
        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
        System.exit(ToolRunner.run(conf, new SC31000303Resolve(), otherArgs));
    }
}
