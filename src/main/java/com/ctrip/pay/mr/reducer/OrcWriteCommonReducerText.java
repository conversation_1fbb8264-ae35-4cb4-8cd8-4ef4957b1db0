package com.ctrip.pay.mr.reducer;

import com.ctrip.pay.mr.map.ComposedKey;
import com.ctrip.pay.mr.utils.MRUtils;
import org.apache.hadoop.io.NullWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;
import org.apache.hadoop.mapreduce.lib.output.MultipleOutputs;
import org.apache.orc.OrcConf;
import org.apache.orc.TypeDescription;
import org.apache.orc.mapred.OrcStruct;
import org.apache.orc.mapred.OrcValue;

import java.io.IOException;

/**
 * @Author: ymfang
 * @Date: 2019/7/18 14:52
 */
public class OrcWriteCommonReducerText
        extends Reducer<Text, OrcValue, NullWritable, OrcStruct> {
    private int count = 0;
    private String schema = "";
    private MultipleOutputs<NullWritable,OrcStruct> multipleOutputs=null;
    private OrcStruct pair = null;

    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        schema = MRUtils.acquireSchema("ORCDescription/servicecode.properties");
        pair=(OrcStruct) OrcStruct.createValue(TypeDescription.fromString(schema));
        multipleOutputs = new MultipleOutputs<NullWritable,OrcStruct>(context);

    }

    @Override
    protected void reduce(Text key, Iterable<OrcValue> values, Context context) throws IOException, InterruptedException {

        String[] keyStr = key.toString().split("_");
        if(keyStr.length!=2){
            return;
        }
        String servicecode = keyStr[0];
        String day = keyStr[1];
        if ((count++) < 3) {
            System.out.println("InnerReducer:" + count + ": uid=" + key.toString()+"  servicecode="+servicecode);
        }
        if(servicecode!=null&&day!=null) {
            for (OrcValue itm : values) {
                OrcConf.MAPRED_SHUFFLE_VALUE_SCHEMA.setString(context.getConfiguration(), schema);
                OrcConf.MAPRED_OUTPUT_SCHEMA.setString(context.getConfiguration(), schema);
                multipleOutputs.write(servicecode, NullWritable.get(), itm.value, "ftutor");
              //  String.format("servicecode=%s/dt=%s", servicecode, day)
            }
        }

    }
    @Override
    public void cleanup(Context context) throws IOException, InterruptedException
    {

        if(null != multipleOutputs) {
            multipleOutputs.close();
            multipleOutputs = null;
        }
        super.cleanup(context);

    }

    public static void main(String[] args) {
        String servicecode = "310021";
        String day = "2019-08-30";
        String str = String.format("servicecode=%s/dt=%s", servicecode, day);
        System.out.println(str);
    }
}
