package com.ctrip.pay.mr.map;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.io.WritableComparable;
import org.apache.hadoop.io.WritableComparator;

/**
 * @Author: ymfang
 * @Date: 2019/8/19 14:52
 */
public class GroupingStragy {
    public static class GroupingComparator extends WritableComparator {
        public GroupingComparator() {
            super(Text.class, true);
        }

        @Override
        public int compare(WritableComparable a, WritableComparable b) {
            Text first = (Text) a;
            Text second = (Text) b;
            return first.toString().substring(0,4).compareTo(second.toString().substring(0,4));
        }
    }
}
