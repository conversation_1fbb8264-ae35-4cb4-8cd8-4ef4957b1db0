//package com.ctrip.pay.mr.utils;
//
//import com.alibaba.fastjson.JSON;
//import com.ctrip.framework.foundation.Foundation;
//import com.ctrip.pay.mr.servicecode.serialize.MessageParser;
//import com.ctrip.pay.mr.servicecode.serialize.TagParser;
//import com.ctrip.pay.mr.servicecode.serialize.TraceModel;
//import com.ctrip.soa.platform.basesystem.emailservice.v1.EmailServiceClient;
//import com.ctrip.soa.platform.basesystem.emailservice.v1.SendEmailRequest;
//import com.ctrip.soa.platform.basesystem.emailservice.v1.SendEmailResponse;
//import com.ctriposs.baiji.rpc.common.logging.Logger;
//import com.ctriposs.baiji.rpc.common.logging.LoggerFactory;
//import org.apache.commons.lang.StringUtils;
//import org.joda.time.DateTime;
//
//import java.util.Arrays;
//import java.util.Locale;
//import java.util.Map;
//
//
//public class EmailUtil {
//
//    private static final Logger logger = LoggerFactory.getLogger(EmailUtil.class);
//
//    public static String MAIL_SEND_CODE = "37030029";
//    public static String MAIL_SENDER = "<EMAIL>";
//    public static int MAIL_BODY_TEMPLATE_ID = 37030029;
//
//
//    public static void sendSimpleEmail(String subject, String message, String mailTos) {
//
//
//        if (StringUtils.isNotBlank(mailTos)) {
//            StringBuilder subjectAndEnv = new StringBuilder().append("环境[").append(Foundation.server().getEnv().getName()).append("]").append(subject);
//            StringBuilder htmlMessage = new StringBuilder();
//            htmlMessage.append("<entry><content><![CDATA[").append(message).append("]]></content></entry>");
//
//            SendEmailRequest mailRequest = new SendEmailRequest();
//            //由邮件通道组分配
//            mailRequest.setSendCode(MAIL_SEND_CODE);
//            //发件应用APPID
//            mailRequest.setAppID(Integer.parseInt(Foundation.app().getAppId()));
//            //发送Email地址
//            mailRequest.setSender(MAIL_SENDER);
//            //接收Email地址
//            mailRequest.setRecipient(Arrays.asList(mailTos.split(",")));
//            //主题
//            mailRequest.setSubject(subjectAndEnv.toString());
//            //由邮件通道组分配
//            mailRequest.setBodyTemplateID(MAIL_BODY_TEMPLATE_ID);
//            //邮件内容
//            mailRequest.setBodyContent(htmlMessage.toString());
//            //是不是HMTL格式的邮件
//            mailRequest.setIsBodyHtml(true);
//            //邮件字符集（GB2312，BIG5,UTF8），默认GB2312
//            mailRequest.setCharset("GB2312");
//            //过期时间，设置为1天
//            DateTime now = new DateTime();
//            DateTime tomorrow = now.plusDays(1);
//            mailRequest.setExpiredTime(tomorrow.toCalendar(Locale.getDefault()));
//
//
//            try {
//                SendEmailResponse mailResponse = sendEmail(mailRequest);
//                if (mailResponse == null) {
//                    logger.error("发送简单邮件失败！mailResponse=null！");
//                } else if (mailResponse.getResultCode() != 1) {
//                    logger.error("发送简单邮件失败！resultCode:" + mailResponse.getResultCode() + ",resultMsg:" + mailResponse.getResultMsg());
//                }
//            } catch (Exception e) {
//                logger.error("发送简单邮件失败！e:" + e);
//            }
//        }
//    }
//
//    public static SendEmailResponse sendEmail(SendEmailRequest request) throws Exception {
//        return getEmailServiceClient().sendEmail(request);
//    }
//
//    private static EmailServiceClient getEmailServiceClient() {
//        EmailServiceClient client = EmailServiceClient.getInstance();
//        client.setRequestTimeout(5000);
//        client.setConnectTimeout(5000);
//        return client;
//    }
//
//
//    public static void main(String[] args) {
//        String oriStr = "[[syscode=09,uid=_FL2914784289,logtype=paymentinfo,servicecode=90200401,guid=携程SQLed66487f-c54c-4562-aa67-1e8d95ccc3a6,serverIp=*************,title=customerTicketVerify,calleetype=customerTicketVerify,maintype=CBU.90200401]]{\\\"serviceName\\\":\\\"customerTicketVerify\\\",\\\"requestHead\\\":{\\\"syscode\\\":\\\"09\\\",\\\"lang\\\":\\\"01\\\",\\\"auth\\\":\\\"9EB21FED3923E02B53809A595E06A7B1A5EDF3F07E93374CF3115A5A68405C09\\\",\\\"appId\\\":\\\"5125\\\",\\\"ctok\\\":\\\"\\\",\\\"cver\\\":\\\"1.0\\\",\\\"sid\\\":\\\"8888\\\",\\\"extension\\\":[{\\\"name\\\":\\\"**h\\\",\\\"value\\\":\\\"9EB21FED3923E02B53809A595E06A7B1A5EDF3F07E93374CF3115A5A68405C09\\\"},{\\\"name\\\":\\\"**e\\\",\\\"value\\\":\\\"ThirdPart\\\"},{\\\"name\\\":\\\"**d\\\",\\\"value\\\":\\\"_FL2914784289\\\"},{\\\"name\\\":\\\"**h\\\",\\\"value\\\":\\\"true\\\"},{\\\"name\\\":\\\"**w\\\",\\\"value\\\":\\\"true\\\"}],\\\"pauth\\\":null,\\\"sauth\\\":null,\\\"appid\\\":null},\\\"request\\\":{\\\"head\\\":{\\\"syscode\\\":\\\"09\\\",\\\"lang\\\":\\\"01\\\",\\\"auth\\\":\\\"9EB21FED3923E02B53809A595E06A7B1A5EDF3F07E93374CF3115A5A68405C09\\\",\\\"cid\\\":\\\"09031037412195853043\\\",\\\"ctok\\\":\\\"\\\",\\\"cver\\\":\\\"1.0\\\",\\\"sid\\\":\\\"8888\\\",\\\"extension\\\":[{\\\"name\\\":\\\"**h\\\",\\\"value\\\":\\\"9EB21FED3923E02B53809A595E06A7B1A5EDF3F07E93374CF3115A5A68405C09\\\"},{\\\"name\\\":\\\"**e\\\",\\\"value\\\":\\\"ThirdPart\\\"},{\\\"name\\\":\\\"**d\\\",\\\"value\\\":\\\"_FL2914784289\\\"},{\\\"name\\\":\\\"**h\\\",\\\"value\\\":\\\"true\\\"},{\\\"name\\\":\\\"**w\\\",\\\"value\\\":\\\"true\\\"}],\\\"pauth\\\":null,\\\"sauth\\\":null,\\\"appid\\\":null},\\\"ver\\\":832000,\\\"plat\\\":5,\\\"pwd\\\":\\\"****w\\\"},\\\"response\\\":{\\\"ResponseStatus\\\":null,\\\"res\\\":true,\\\"rcode\\\":0,\\\"msg\\\":\\\"成功\\\"},\\\"beginTime\\\":\\\"2021-12-13 20:12:25.639\\\",\\\"endTime\\\":\\\"2021-12-13 20:12:25.670\\\",\\\"timeSpan\\\":31,\\\"appId\\\":\\\"100006883\\\"}";
//        String msg = MessageParser.parseMsg(oriStr);
//        System.out.println("msg :" + msg);
//        Map<String, String> tagsMap = TagParser.parseTags(msg);
//        System.out.println("tagsMap :" + tagsMap.toString());
//
//        String bodyStr = MessageParser.parseBody(msg);
//        System.out.println("bodyStr :" + bodyStr);
//
//        TraceModel traceModel = JSON.parseObject(bodyStr, TraceModel.class);
//        System.out.println("traceModel :" + traceModel.toString());
//
//    }
//}
